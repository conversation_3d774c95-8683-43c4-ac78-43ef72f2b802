<SEC-DOCUMENT>0001560385-19-000034.txt : 20191126
<SEC-HEADER>0001560385-19-000034.hdr.sgml : 20191126
<ACCEPTANCE-DATETIME>20191126163827
ACCESSION NUMBER:		0001560385-19-000034
CONFORMED SUBMISSION TYPE:	8-K
PUBLIC DOCUMENT COUNT:		14
CONFORMED PERIOD OF REPORT:	20191126
ITEM INFORMATION:		Regulation FD Disclosure
ITEM INFORMATION:		Financial Statements and Exhibits
FILED AS OF DATE:		20191126
DATE AS OF CHANGE:		20191126

FILER:

	COMPANY DATA:	
		COMPANY CONFORMED NAME:			Liberty Media Corp
		CENTRAL INDEX KEY:			0001560385
		STANDARD INDUSTRIAL CLASSIFICATION:	TELEVISION BROADCASTING STATIONS [4833]
		IRS NUMBER:				*********
		STATE OF INCORPORATION:			DE
		FISCAL YEAR END:			1231

	FILING VALUES:
		FORM TYPE:		8-K
		SEC ACT:		1934 Act
		SEC FILE NUMBER:	001-35707
		FILM NUMBER:		*********

	BUSINESS ADDRESS:	
		STREET 1:		12300 LIBERTY BOULEVARD
		CITY:			ENGLEWOOD
		STATE:			CO
		ZIP:			80112
		BUSINESS PHONE:		************

	MAIL ADDRESS:	
		STREET 1:		12300 LIBERTY BOULEVARD
		CITY:			ENGLEWOOD
		STATE:			CO
		ZIP:			80112

	FORMER COMPANY:	
		FORMER CONFORMED NAME:	Liberty Spinco, Inc.
		DATE OF NAME CHANGE:	20121015
</SEC-HEADER>
<DOCUMENT>
<TYPE>8-K
<SEQUENCE>1
<FILENAME>lmca-20191126x8k.htm
<DESCRIPTION>8-K
<TEXT>
<XBRL>
<?xml version='1.0' encoding='UTF-8'?>

      <!-- iXBRL document created with: Toppan Merrill Bridge iXBRL 9.4.7193.41938 -->
      <!-- Based on: iXBRL 1.1 -->
      <!-- Created on: 11/26/2019 9:35:32 PM -->
      <!-- iXBRL Library version: 1.0.7193.41955 -->
      <!-- iXBRL Service Job ID: 35ea73ff-f3ad-4ccb-bebb-32971c326444 -->

  <html xmlns:us-gaap="http://fasb.org/us-gaap/2019-01-31" xmlns:lmca="http://www.libertymedia.com/20191126" xmlns:link="http://www.xbrl.org/2003/linkbase" xmlns:xbrldt="http://xbrl.org/2005/xbrldt" xmlns:ixt-sec="http://www.sec.gov/inlineXBRL/transformation/2015-08-31" xmlns:ix="http://www.xbrl.org/2013/inlineXBRL" xmlns:ref="http://www.xbrl.org/2006/ref" xmlns:xbrli="http://www.xbrl.org/2003/instance" xmlns:dei="http://xbrl.sec.gov/dei/2019-01-31" xmlns="http://www.w3.org/1999/xhtml" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xmlns:ixt="http://www.xbrl.org/inlineXBRL/transformation/2015-02-26" xmlns:xbrldi="http://xbrl.org/2006/xbrldi"><head><meta content="text/html" http-equiv="content-type" /><title></title></head><body><div style="margin-top:30pt;"></div><div style="display:none;"><ix:header><ix:hidden><ix:nonNumeric contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA" name="dei:EntityCentralIndexKey" id="Hidden_hjsUU4QfPUWwJcn7bkI3sw">0001560385</ix:nonNumeric><ix:nonNumeric contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA" name="dei:AmendmentFlag" id="Hidden_P2gAuOCpCESucPhx-0_XoQ">false</ix:nonNumeric></ix:hidden><ix:references><link:schemaRef xlink:type="simple" xlink:href="lmca-20191126.xsd"></link:schemaRef></ix:references><ix:resources><xbrli:context id="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertySiriusXmGroupCommonClassMember_JAHYIrCcpE2AfOW2m-MR9A"><xbrli:entity><xbrli:identifier scheme="http://www.sec.gov/CIK">0001560385</xbrli:identifier><xbrli:segment><xbrldi:explicitMember dimension="us-gaap:StatementClassOfStockAxis">lmca:LibertySiriusXmGroupCommonClassMember</xbrldi:explicitMember></xbrli:segment></xbrli:entity><xbrli:period><xbrli:startDate>2019-11-26</xbrli:startDate><xbrli:endDate>2019-11-26</xbrli:endDate></xbrli:period></xbrli:context><xbrli:context id="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertySiriusXmGroupCommonClassCMember_8SciBKG1tUqNetlucqFpLg"><xbrli:entity><xbrli:identifier scheme="http://www.sec.gov/CIK">0001560385</xbrli:identifier><xbrli:segment><xbrldi:explicitMember dimension="us-gaap:StatementClassOfStockAxis">lmca:LibertySiriusXmGroupCommonClassCMember</xbrldi:explicitMember></xbrli:segment></xbrli:entity><xbrli:period><xbrli:startDate>2019-11-26</xbrli:startDate><xbrli:endDate>2019-11-26</xbrli:endDate></xbrli:period></xbrli:context><xbrli:context id="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertySiriusXmGroupCommonClassBMember_LcWuYDm5dEWjseRLNV3nmw"><xbrli:entity><xbrli:identifier scheme="http://www.sec.gov/CIK">0001560385</xbrli:identifier><xbrli:segment><xbrldi:explicitMember dimension="us-gaap:StatementClassOfStockAxis">lmca:LibertySiriusXmGroupCommonClassBMember</xbrldi:explicitMember></xbrli:segment></xbrli:entity><xbrli:period><xbrli:startDate>2019-11-26</xbrli:startDate><xbrli:endDate>2019-11-26</xbrli:endDate></xbrli:period></xbrli:context><xbrli:context id="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertyFormulaOneGroupCommonClassMember_Qku0i7SRpkKw2eLBgjmTUQ"><xbrli:entity><xbrli:identifier scheme="http://www.sec.gov/CIK">0001560385</xbrli:identifier><xbrli:segment><xbrldi:explicitMember dimension="us-gaap:StatementClassOfStockAxis">lmca:LibertyFormulaOneGroupCommonClassMember</xbrldi:explicitMember></xbrli:segment></xbrli:entity><xbrli:period><xbrli:startDate>2019-11-26</xbrli:startDate><xbrli:endDate>2019-11-26</xbrli:endDate></xbrli:period></xbrli:context><xbrli:context id="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertyFormulaOneGroupCommonClassCMember__WB6cnMrU0Cgg-JQk9l8tA"><xbrli:entity><xbrli:identifier scheme="http://www.sec.gov/CIK">0001560385</xbrli:identifier><xbrli:segment><xbrldi:explicitMember dimension="us-gaap:StatementClassOfStockAxis">lmca:LibertyFormulaOneGroupCommonClassCMember</xbrldi:explicitMember></xbrli:segment></xbrli:entity><xbrli:period><xbrli:startDate>2019-11-26</xbrli:startDate><xbrli:endDate>2019-11-26</xbrli:endDate></xbrli:period></xbrli:context><xbrli:context id="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertyBravesGroupCommonClassMember_sm1mh0XjS0-TMjZL81FbQA"><xbrli:entity><xbrli:identifier scheme="http://www.sec.gov/CIK">0001560385</xbrli:identifier><xbrli:segment><xbrldi:explicitMember dimension="us-gaap:StatementClassOfStockAxis">lmca:LibertyBravesGroupCommonClassMember</xbrldi:explicitMember></xbrli:segment></xbrli:entity><xbrli:period><xbrli:startDate>2019-11-26</xbrli:startDate><xbrli:endDate>2019-11-26</xbrli:endDate></xbrli:period></xbrli:context><xbrli:context id="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertyBravesGroupCommonClassCMember_Fl2DCyEN9Uuz5sCVzEVhwQ"><xbrli:entity><xbrli:identifier scheme="http://www.sec.gov/CIK">0001560385</xbrli:identifier><xbrli:segment><xbrldi:explicitMember dimension="us-gaap:StatementClassOfStockAxis">lmca:LibertyBravesGroupCommonClassCMember</xbrldi:explicitMember></xbrli:segment></xbrli:entity><xbrli:period><xbrli:startDate>2019-11-26</xbrli:startDate><xbrli:endDate>2019-11-26</xbrli:endDate></xbrli:period></xbrli:context><xbrli:context id="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA"><xbrli:entity><xbrli:identifier scheme="http://www.sec.gov/CIK">0001560385</xbrli:identifier></xbrli:entity><xbrli:period><xbrli:startDate>2019-11-26</xbrli:startDate><xbrli:endDate>2019-11-26</xbrli:endDate></xbrli:period></xbrli:context></ix:resources></ix:header></div><div style="max-width:100%;padding-left:5.88%;padding-right:5.88%;position:relative;"><div style="clear:both;max-width:100%;position:relative;"><a id="_8d8b4af5_e93e_40cd_8cc2_fd13e5f9a2ae"></a><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.43;text-align:center;margin:0pt;"><span style="font-size:11pt;">UNITED STATES</span></p><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.43;text-align:center;margin:0pt;"><span style="font-size:11pt;">SECURITIES AND EXCHANGE COMMISSION</span></p><p style="font-family:'Times New Roman';font-size:10pt;text-align:center;margin:0pt;"><span style="visibility:hidden;">&#8203;</span></p><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.43;text-align:center;margin:0pt;"><span style="font-size:11pt;">Washington, D.C. 20549</span></p><p style="font-family:'Times New Roman';font-size:10pt;text-align:center;margin:0pt;"><span style="visibility:hidden;">&#8203;</span></p><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.43;text-align:center;margin:0pt;"><b style="font-size:11pt;font-weight:bold;">FORM </b><ix:nonNumeric contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA" name="dei:DocumentType" id="Narr_RKB_PDK8ZUmQPPvUmWvCoQ"><b style="font-size:11pt;font-weight:bold;">8-K</b></ix:nonNumeric></p><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.43;text-align:center;margin:0pt;"><b style="font-size:11pt;font-weight:bold;">CURRENT REPORT</b></p><p style="font-family:'Times New Roman';font-size:10pt;text-align:center;margin:0pt;"><span style="visibility:hidden;">&#8203;</span></p><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.43;text-align:center;margin:0pt;"><span style="font-size:11pt;">Pursuant to Section 13 or 15(d)</span></p><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.43;text-align:center;margin:0pt;"><span style="font-size:11pt;">of the Securities Exchange Act of 1934</span></p><p style="font-family:'Times New Roman';font-size:10pt;margin:0pt;"><span style="visibility:hidden;">&#8203;</span></p><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.43;text-align:center;margin:0pt;"><span style="font-size:11pt;">Date of Report (date of earliest event reported): </span><ix:nonNumeric format="ixt:datemonthdayyearen" contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA" name="dei:DocumentPeriodEndDate" id="Narr_DLyQyteFGkGM4fFBiu3DLQ"><b style="font-size:11pt;font-weight:bold;">November 26, 2019</b></ix:nonNumeric></p><p style="font-family:'Times New Roman';font-size:10pt;text-align:center;margin:0pt;"><span style="visibility:hidden;">&#8203;</span></p><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.43;text-align:center;margin:0pt;"><ix:nonNumeric contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA" name="dei:EntityRegistrantName" id="Narr_Y66LzTeHvEa0pQfdBDlqog"><b style="font-size:11pt;font-weight:bold;">LIBERTY MEDIA CORPORATION</b></ix:nonNumeric></p><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.43;text-align:center;margin:0pt;"><span style="font-size:11pt;">(Exact name of registrant as specified in its charter)</span></p><p style="font-family:'Times New Roman';font-size:10pt;text-align:center;margin:0pt;"></p><a id="_58707859_8d8f_4557_b36f_fc4e53ba6019"></a><a id="Tc_Uumh2iHutUmewZ62KqPwvA_2_0"></a><a id="Tc_xcFRcPchPkOu5kqby3emzw_2_1"></a><a id="Tc_04N_y2fYnU-vLd8tK7BLkg_2_2"></a><table style="border-collapse:collapse;font-size:16pt;margin-left:auto;margin-right:auto;padding-left:0.5pt;padding-right:0.5pt;width:100%;"><tr style="height:1pt;"><td style="vertical-align:bottom;width:35.05%;margin:0pt;padding:0pt 3pt 2pt 3pt;"><div style="height:1pt;overflow:hidden;overflow-wrap:break-word;position:relative;"><div style="bottom:0pt;position:absolute;width:100%;"><p style="font-family:'Times New Roman';font-size:10pt;margin:0pt;"><span style="font-size:1pt;visibility:hidden;">&#8203;</span></p></div></div></td><td style="vertical-align:bottom;width:32%;margin:0pt;padding:0pt 3pt 2pt 3pt;"><div style="height:1pt;overflow:hidden;overflow-wrap:break-word;position:relative;"><div style="bottom:0pt;position:absolute;width:100%;"><p style="font-family:'Times New Roman';font-size:10pt;margin:0pt;"><span style="font-size:1pt;visibility:hidden;">&#8203;</span></p></div></div></td><td style="vertical-align:bottom;width:32.94%;margin:0pt;padding:0pt 3pt 2pt 3pt;"><div style="height:1pt;overflow:hidden;overflow-wrap:break-word;position:relative;"><div style="bottom:0pt;position:absolute;width:100%;"><p style="font-family:'Times New Roman';font-size:10pt;margin:0pt;"><span style="font-size:1pt;visibility:hidden;">&#8203;</span></p></div></div></td></tr><tr><td style="vertical-align:bottom;width:35.05%;margin:0pt;padding:0pt 3pt 2pt 3pt;"><p style="font-family:'Times New Roman';font-size:10pt;text-align:center;margin:0pt;"><ix:nonNumeric format="ixt-sec:stateprovnameen" contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA" name="dei:EntityIncorporationStateCountryCode" id="Tc_NtihsclyCUu0yIawXJWv0w_1_0"><b style="font-size:11pt;font-weight:bold;">Delaware</b></ix:nonNumeric></p></td><td style="vertical-align:bottom;width:32%;margin:0pt;padding:0pt 3pt 2pt 3pt;"><p style="font-family:'Times New Roman';font-size:10pt;text-align:center;margin:0pt;"><ix:nonNumeric contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA" name="dei:EntityFileNumber" id="Tc_NX1wdXBOcUiJ_v5eVGEf2g_1_1"><b style="font-weight:bold;">001-35707</b></ix:nonNumeric></p></td><td style="vertical-align:bottom;width:32.94%;margin:0pt;padding:0pt 3pt 2pt 3pt;"><p style="font-family:'Times New Roman';font-size:10pt;text-align:center;margin:0pt;"><ix:nonNumeric contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA" name="dei:EntityTaxIdentificationNumber" id="Tc_cGp8XFqTUkqV5EMmHqzZ6A_1_2"><b style="font-weight:bold;">37-1699499</b></ix:nonNumeric></p></td></tr><tr><td style="vertical-align:bottom;width:35.05%;margin:0pt;padding:0pt 3pt 2pt 3pt;"><p style="font-family:'Times New Roman';font-size:10pt;text-align:center;margin:0pt;"><span style="font-size:11pt;">(State or other jurisdiction of</span></p><p style="font-family:'Times New Roman';font-size:10pt;text-align:center;margin:0pt;"><span style="font-size:11pt;">incorporation or organization)</span></p></td><td style="vertical-align:bottom;width:32%;margin:0pt;padding:0pt 3pt 2pt 3pt;"><p style="font-family:'Times New Roman';font-size:10pt;text-align:center;margin:0pt;"><span style="font-size:11pt;">(Commission</span></p><p style="font-family:'Times New Roman';font-size:10pt;text-align:center;margin:0pt;"><span style="font-size:11pt;">File Number)</span></p></td><td style="vertical-align:bottom;width:32.94%;margin:0pt;padding:0pt 3pt 2pt 3pt;"><p style="font-family:'Times New Roman';font-size:10pt;text-align:center;margin:0pt;"><span style="font-size:11pt;">(I.R.S. Employer</span></p><p style="font-family:'Times New Roman';font-size:10pt;text-align:center;margin:0pt;"><span style="font-size:11pt;">Identification No.)</span></p></td></tr></table><p style="font-family:'Times New Roman';font-size:10pt;text-align:center;margin:0pt;"><span style="visibility:hidden;">&#8203;</span></p><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.43;text-align:center;margin:0pt;"><ix:nonNumeric contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA" name="dei:EntityAddressAddressLine1" id="Narr_KZ4ZlOlv_0a-k23MDZ2G4Q"><b style="font-weight:bold;">12300 Liberty Blvd.</b></ix:nonNumeric></p><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.43;text-align:center;margin:0pt;"><ix:nonNumeric contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA" name="dei:EntityAddressCityOrTown" id="Narr_3HFyPjGH6USH-12UOJ37LQ"><b style="font-weight:bold;">Englewood</b></ix:nonNumeric><b style="font-weight:bold;">, </b><ix:nonNumeric format="ixt-sec:stateprovnameen" contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA" name="dei:EntityAddressStateOrProvince" id="Narr_MIn4r5xA4kyjxecO9KlY-Q"><b style="font-weight:bold;">Colorado</b></ix:nonNumeric><b style="font-weight:bold;"> </b><ix:nonNumeric contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA" name="dei:EntityAddressPostalZipCode" id="Narr_8CGdm8GC7ESac_MSDmUVow"><b style="font-weight:bold;">80112</b></ix:nonNumeric></p><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.43;text-align:center;margin:0pt;">(Address of principal executive offices and zip code)</p><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.43;text-align:center;margin:0pt 0pt 6pt 0pt;">Registrant&#39;s telephone number, including area code: <b style="font-weight:bold;">(</b><ix:nonNumeric contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA" name="dei:CityAreaCode" id="Narr_75nxv9bDJUOme3cQoZX_sw"><b style="font-weight:bold;">720</b></ix:nonNumeric><b style="font-weight:bold;">) </b><ix:nonNumeric contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA" name="dei:LocalPhoneNumber" id="Narr_RIstD-bHD0qdgN9_HpUf6A"><b style="font-weight:bold;">875-5400</b></ix:nonNumeric></p><p style="font-family:'Times New Roman';font-size:9pt;line-height:1.43;margin:0pt 0pt 6pt 0pt;">Check the appropriate box below if the Form 8-K filing is intended to simultaneously satisfy the filing obligation of the registrant under any of the following provisions (see General Instruction A.2. below):</p><p style="font-family:'Times New Roman';font-size:9pt;line-height:1.43;margin:3pt 0pt 0pt 0pt;"><ix:nonNumeric format="ixt-sec:boolballotbox" contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA" name="dei:WrittenCommunications" id="Narr_W-8Wkfvr7UyAM0hcEso25Q"><span style="font-family:'Segoe UI Symbol';">&#9744;</span></ix:nonNumeric> Written communications pursuant to Rule 425 under the Securities Act (17 CFR 230.425)</p><p style="font-family:'Times New Roman';font-size:9pt;line-height:1.43;margin:3pt 0pt 0pt 0pt;"><ix:nonNumeric format="ixt-sec:boolballotbox" contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA" name="dei:SolicitingMaterial" id="Narr_e7HkfBUVY02mB1hpWafbag"><span style="font-family:'Segoe UI Symbol';">&#9744;</span></ix:nonNumeric> Soliciting material pursuant to Rule 14a-12 under the Exchange Act (17 CFR 240.14a-12)</p><p style="font-family:'Times New Roman';font-size:9pt;line-height:1.43;margin:3pt 0pt 0pt 0pt;"><ix:nonNumeric format="ixt-sec:boolballotbox" contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA" name="dei:PreCommencementTenderOffer" id="Narr_JAla5rUS1kSpuM0VfjcxfQ"><span style="font-family:'Segoe UI Symbol';">&#9744;</span></ix:nonNumeric> Pre-commencement communications pursuant to Rule 14d-2(b) under the Exchange Act (17 CFR 240.14d-2(b))</p><p style="font-family:'Times New Roman';font-size:9pt;line-height:1.43;margin:3pt 0pt 0pt 0pt;"><ix:nonNumeric format="ixt-sec:boolballotbox" contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA" name="dei:PreCommencementIssuerTenderOffer" id="Narr_KLH_CKJpgUmtzK9Gi4G7-Q"><span style="font-family:'Segoe UI Symbol';">&#9744;</span></ix:nonNumeric> Pre-commencement communications pursuant to Rule 13e-4(c) under the Exchange Act (17 CFR 240.13e-4(c))</p><p style="font-family:'Times New Roman';font-size:9pt;line-height:1.43;margin:3pt 0pt 0pt 0pt;"><span style="white-space:pre-wrap;"> Securities registered pursuant to Section 12(b) of the Act:</span></p><a id="_35b72111_1338_4605_95d5_b6f5b8b6c4c4"></a><a id="Tc_SX3t3HiYJEi1y6AXOojwiA_0_0"></a><a id="Tc_DZvXLpoa8UOW_ZelPfLYSg_1_0"></a><a id="Tc_dQHv4_y7IU-k6ciwmgNibg_1_1"></a><a id="Tc_e2hFPJNT0UWXtGnKDKuUNg_1_2"></a><table style="border-collapse:collapse;font-size:16pt;margin-left:auto;margin-right:auto;width:99.89%;"><tr style="height:4pt;"><td style="vertical-align:top;width:39.92%;margin:0pt;padding:0pt 5.4pt 0pt 5.4pt;"><div style="height:4pt;overflow:hidden;overflow-wrap:break-word;position:relative;"><div style="position:absolute;top:0pt;width:100%;"><p style="font-family:'Times New Roman';font-size:1pt;margin:0pt;">T</p></div></div></td><td style="vertical-align:top;width:20.41%;margin:0pt;padding:0pt 5.4pt 0pt 5.4pt;"><div style="height:4pt;overflow:hidden;overflow-wrap:break-word;position:relative;"><div style="position:absolute;top:0pt;width:100%;"><p style="font-family:'Times New Roman';font-size:10pt;margin:0pt;"><span style="font-size:1pt;visibility:hidden;">&#8203;</span></p></div></div></td><td style="vertical-align:top;width:39.66%;margin:0pt;padding:0pt 5.4pt 0pt 5.4pt;"><div style="height:4pt;overflow:hidden;overflow-wrap:break-word;position:relative;"><div style="position:absolute;top:0pt;width:100%;"><p style="font-family:'Times New Roman';font-size:10pt;margin:0pt;"><span style="font-size:1pt;visibility:hidden;">&#8203;</span></p></div></div></td></tr><tr style="height:14.4pt;"><td style="vertical-align:top;width:39.92%;border-bottom:1px solid #000000;border-left:1px solid #000000;border-right:1px solid #000000;border-top:1px solid #000000;margin:0pt;padding:0pt 5.15pt 0pt 5.15pt;"><div style="height:14.4pt;overflow:hidden;overflow-wrap:break-word;position:relative;"><div style="position:absolute;top:0pt;width:100%;"><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.43;text-align:center;margin:0pt 0pt 6pt 0pt;"><span style="line-height:1.2;">Title of each class </span></p></div></div></td><td style="vertical-align:top;width:20.41%;border-bottom:1px solid #000000;border-left:1px solid #000000;border-right:1px solid #000000;border-top:1px solid #000000;margin:0pt;padding:0pt 5.15pt 0pt 5.15pt;"><div style="height:14.4pt;overflow:hidden;overflow-wrap:break-word;position:relative;"><div style="position:absolute;top:0pt;width:100%;"><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.43;text-align:center;margin:0pt 0pt 6pt 0pt;"><span style="line-height:1.2;">Trading Symbol</span></p></div></div></td><td style="vertical-align:top;width:39.66%;border-bottom:1px solid #000000;border-left:1px solid #000000;border-right:1px solid #000000;border-top:1px solid #000000;margin:0pt;padding:0pt 5.15pt 0pt 5.15pt;"><div style="height:14.4pt;overflow:hidden;overflow-wrap:break-word;position:relative;"><div style="position:absolute;top:0pt;width:100%;"><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.43;text-align:center;margin:0pt 0pt 6pt 0pt;"><span style="line-height:1.2;">Name of each exchange on which registered </span></p></div></div></td></tr><tr style="height:14.4pt;"><td style="vertical-align:middle;width:39.92%;border-bottom:1px solid #000000;border-left:1px solid #000000;border-right:1px solid #000000;border-top:1px solid #000000;margin:0pt;padding:0pt 5.15pt 0pt 5.15pt;"><div style="height:14.4pt;overflow:hidden;overflow-wrap:break-word;position:relative;"><div style="position:absolute;top:50%;transform:translate(0,-50%);width:100%;"><p style="font-family:'Times New Roman';font-size:9pt;line-height:1.43;text-align:center;margin:0pt 0pt 6pt 0pt;"><ix:nonNumeric contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertySiriusXmGroupCommonClassMember_JAHYIrCcpE2AfOW2m-MR9A" name="dei:Security12bTitle" id="Tc_CaSaROAVBUmBhW0Lf32ROQ_2_0"><span style="line-height:1.2;">Series A Liberty SiriusXM Common Stock </span></ix:nonNumeric></p></div></div></td><td style="vertical-align:middle;width:20.41%;border-bottom:1px solid #000000;border-left:1px solid #000000;border-right:1px solid #000000;border-top:1px solid #000000;margin:0pt;padding:0pt 5.15pt 0pt 5.15pt;"><div style="height:14.4pt;overflow:hidden;overflow-wrap:break-word;position:relative;"><div style="position:absolute;top:50%;transform:translate(0,-50%);width:100%;"><p style="font-family:'Times New Roman';font-size:9pt;line-height:1.43;text-align:center;margin:0pt 0pt 6pt 0pt;"><ix:nonNumeric contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertySiriusXmGroupCommonClassMember_JAHYIrCcpE2AfOW2m-MR9A" name="dei:TradingSymbol" id="Tc_-BIVI0LdzkuFglHM--dXSQ_2_1"><span style="line-height:1.2;">LSXMA</span></ix:nonNumeric></p></div></div></td><td style="vertical-align:middle;width:39.66%;border-bottom:1px solid #000000;border-left:1px solid #000000;border-right:1px solid #000000;border-top:1px solid #000000;margin:0pt;padding:0pt 5.15pt 0pt 5.15pt;"><div style="height:14.4pt;overflow:hidden;overflow-wrap:break-word;position:relative;"><div style="position:absolute;top:50%;transform:translate(0,-50%);width:100%;"><p style="font-family:'Times New Roman';font-size:9pt;line-height:1.43;text-align:center;margin:0pt 0pt 6pt 0pt;"><ix:nonNumeric format="ixt-sec:exchnameen" contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertySiriusXmGroupCommonClassMember_JAHYIrCcpE2AfOW2m-MR9A" name="dei:SecurityExchangeName" id="Tc_EAjVmo6NSU-sZJeLvqOLKg_2_2"><span style="line-height:1.2;">The Nasdaq Stock Market LLC</span></ix:nonNumeric></p></div></div></td></tr><tr style="height:14.4pt;"><td style="vertical-align:middle;width:39.92%;border-bottom:1px solid #000000;border-left:1px solid #000000;border-right:1px solid #000000;border-top:1px solid #000000;margin:0pt;padding:0pt 5.15pt 0pt 5.15pt;"><div style="height:14.4pt;overflow:hidden;overflow-wrap:break-word;position:relative;"><div style="position:absolute;top:50%;transform:translate(0,-50%);width:100%;"><p style="font-family:'Times New Roman';font-size:9pt;line-height:1.43;text-align:center;margin:0pt 0pt 6pt 0pt;"><ix:nonNumeric contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertySiriusXmGroupCommonClassBMember_LcWuYDm5dEWjseRLNV3nmw" name="dei:Security12bTitle" id="Tc_GX5JeY8NH0OKDCx6ZVghtA_3_0"><span style="line-height:1.2;">Series B Liberty SiriusXM Common Stock</span></ix:nonNumeric></p></div></div></td><td style="vertical-align:middle;width:20.41%;border-bottom:1px solid #000000;border-left:1px solid #000000;border-right:1px solid #000000;border-top:1px solid #000000;margin:0pt;padding:0pt 5.15pt 0pt 5.15pt;"><div style="height:14.4pt;overflow:hidden;overflow-wrap:break-word;position:relative;"><div style="position:absolute;top:50%;transform:translate(0,-50%);width:100%;"><p style="font-family:'Times New Roman';font-size:9pt;line-height:1.43;text-align:center;margin:0pt 0pt 6pt 0pt;"><ix:nonNumeric contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertySiriusXmGroupCommonClassBMember_LcWuYDm5dEWjseRLNV3nmw" name="dei:TradingSymbol" id="Tc_RSiz3YNm-ECVXDyNIFktMQ_3_1"><span style="line-height:1.2;">LSXMB</span></ix:nonNumeric></p></div></div></td><td style="vertical-align:middle;width:39.66%;border-bottom:1px solid #000000;border-left:1px solid #000000;border-right:1px solid #000000;border-top:1px solid #000000;margin:0pt;padding:0pt 5.15pt 0pt 5.15pt;"><div style="height:14.4pt;overflow:hidden;overflow-wrap:break-word;position:relative;"><div style="position:absolute;top:50%;transform:translate(0,-50%);width:100%;"><p style="font-family:'Times New Roman';font-size:9pt;line-height:1.43;text-align:center;margin:0pt 0pt 6pt 0pt;"><ix:nonNumeric format="ixt-sec:exchnameen" contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertySiriusXmGroupCommonClassBMember_LcWuYDm5dEWjseRLNV3nmw" name="dei:SecurityExchangeName" id="Tc_-ie0s4MQ-EaNii9Yv-R8mw_3_2"><span style="line-height:1.2;">The Nasdaq Stock Market LLC</span></ix:nonNumeric></p></div></div></td></tr><tr style="height:14.4pt;"><td style="vertical-align:middle;width:39.92%;border-bottom:1px solid #000000;border-left:1px solid #000000;border-right:1px solid #000000;border-top:1px solid #000000;margin:0pt;padding:0pt 5.15pt 0pt 5.15pt;"><div style="height:14.4pt;overflow:hidden;overflow-wrap:break-word;position:relative;"><div style="position:absolute;top:50%;transform:translate(0,-50%);width:100%;"><p style="font-family:'Times New Roman';font-size:9pt;line-height:1.43;text-align:center;margin:0pt 0pt 6pt 0pt;"><ix:nonNumeric contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertySiriusXmGroupCommonClassCMember_8SciBKG1tUqNetlucqFpLg" name="dei:Security12bTitle" id="Tc_mQidI1qm4EiyG1QXy3aR3Q_4_0"><span style="line-height:1.2;">Series C Liberty SiriusXM Common Stock</span></ix:nonNumeric></p></div></div></td><td style="vertical-align:middle;width:20.41%;border-bottom:1px solid #000000;border-left:1px solid #000000;border-right:1px solid #000000;border-top:1px solid #000000;margin:0pt;padding:0pt 5.15pt 0pt 5.15pt;"><div style="height:14.4pt;overflow:hidden;overflow-wrap:break-word;position:relative;"><div style="position:absolute;top:50%;transform:translate(0,-50%);width:100%;"><p style="font-family:'Times New Roman';font-size:9pt;line-height:1.43;text-align:center;margin:0pt 0pt 6pt 0pt;"><ix:nonNumeric contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertySiriusXmGroupCommonClassCMember_8SciBKG1tUqNetlucqFpLg" name="dei:TradingSymbol" id="Tc_OIuXa3V04kWLW4OhogrQeA_4_1"><span style="line-height:1.2;">LSXMK</span></ix:nonNumeric></p></div></div></td><td style="vertical-align:middle;width:39.66%;border-bottom:1px solid #000000;border-left:1px solid #000000;border-right:1px solid #000000;border-top:1px solid #000000;margin:0pt;padding:0pt 5.15pt 0pt 5.15pt;"><div style="height:14.4pt;overflow:hidden;overflow-wrap:break-word;position:relative;"><div style="position:absolute;top:50%;transform:translate(0,-50%);width:100%;"><p style="font-family:'Times New Roman';font-size:9pt;line-height:1.43;text-align:center;margin:0pt 0pt 6pt 0pt;"><ix:nonNumeric format="ixt-sec:exchnameen" contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertySiriusXmGroupCommonClassCMember_8SciBKG1tUqNetlucqFpLg" name="dei:SecurityExchangeName" id="Tc_7wclcA0jUky_2JrypKKiPA_4_2"><span style="line-height:1.2;">The Nasdaq Stock Market LLC</span></ix:nonNumeric></p></div></div></td></tr><tr style="height:14.4pt;"><td style="vertical-align:middle;width:39.92%;border-bottom:1px solid #000000;border-left:1px solid #000000;border-right:1px solid #000000;border-top:1px solid #000000;margin:0pt;padding:0pt 5.15pt 0pt 5.15pt;"><div style="height:14.4pt;overflow:hidden;overflow-wrap:break-word;position:relative;"><div style="position:absolute;top:50%;transform:translate(0,-50%);width:100%;"><p style="font-family:'Times New Roman';font-size:9pt;line-height:1.43;text-align:center;margin:0pt 0pt 6pt 0pt;"><ix:nonNumeric contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertyBravesGroupCommonClassMember_sm1mh0XjS0-TMjZL81FbQA" name="dei:Security12bTitle" id="Tc_X65jgshJZUGMTaRjNs9Mlg_5_0"><span style="line-height:1.2;">Series A Liberty Braves Common Stock</span></ix:nonNumeric></p></div></div></td><td style="vertical-align:middle;width:20.41%;border-bottom:1px solid #000000;border-left:1px solid #000000;border-right:1px solid #000000;border-top:1px solid #000000;margin:0pt;padding:0pt 5.15pt 0pt 5.15pt;"><div style="height:14.4pt;overflow:hidden;overflow-wrap:break-word;position:relative;"><div style="position:absolute;top:50%;transform:translate(0,-50%);width:100%;"><p style="font-family:'Times New Roman';font-size:9pt;line-height:1.43;text-align:center;margin:0pt 0pt 6pt 0pt;"><ix:nonNumeric contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertyBravesGroupCommonClassMember_sm1mh0XjS0-TMjZL81FbQA" name="dei:TradingSymbol" id="Tc_NdL2aG1hiUyi2dmkZFZS7Q_5_1"><span style="line-height:1.2;">BATRA</span></ix:nonNumeric></p></div></div></td><td style="vertical-align:middle;width:39.66%;border-bottom:1px solid #000000;border-left:1px solid #000000;border-right:1px solid #000000;border-top:1px solid #000000;margin:0pt;padding:0pt 5.15pt 0pt 5.15pt;"><div style="height:14.4pt;overflow:hidden;overflow-wrap:break-word;position:relative;"><div style="position:absolute;top:50%;transform:translate(0,-50%);width:100%;"><p style="font-family:'Times New Roman';font-size:9pt;line-height:1.43;text-align:center;margin:0pt 0pt 6pt 0pt;"><ix:nonNumeric format="ixt-sec:exchnameen" contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertyBravesGroupCommonClassMember_sm1mh0XjS0-TMjZL81FbQA" name="dei:SecurityExchangeName" id="Tc_Zg4loVvzVUueA64cIVD5wQ_5_2"><span style="line-height:1.2;">The Nasdaq Stock Market LLC</span></ix:nonNumeric></p></div></div></td></tr><tr style="height:14.4pt;"><td style="vertical-align:middle;width:39.92%;border-bottom:1px solid #000000;border-left:1px solid #000000;border-right:1px solid #000000;border-top:1px solid #000000;margin:0pt;padding:0pt 5.15pt 0pt 5.15pt;"><div style="height:14.4pt;overflow:hidden;overflow-wrap:break-word;position:relative;"><div style="position:absolute;top:50%;transform:translate(0,-50%);width:100%;"><p style="font-family:'Times New Roman';font-size:9pt;line-height:1.43;text-align:center;margin:0pt 0pt 6pt 0pt;"><ix:nonNumeric contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertyBravesGroupCommonClassCMember_Fl2DCyEN9Uuz5sCVzEVhwQ" name="dei:Security12bTitle" id="Tc_H8deX0NCnEmDwoFls9AH6w_6_0"><span style="line-height:1.2;">Series C Liberty Braves Common Stock</span></ix:nonNumeric></p></div></div></td><td style="vertical-align:middle;width:20.41%;border-bottom:1px solid #000000;border-left:1px solid #000000;border-right:1px solid #000000;border-top:1px solid #000000;margin:0pt;padding:0pt 5.15pt 0pt 5.15pt;"><div style="height:14.4pt;overflow:hidden;overflow-wrap:break-word;position:relative;"><div style="position:absolute;top:50%;transform:translate(0,-50%);width:100%;"><p style="font-family:'Times New Roman';font-size:9pt;line-height:1.43;text-align:center;margin:0pt 0pt 6pt 0pt;"><ix:nonNumeric contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertyBravesGroupCommonClassCMember_Fl2DCyEN9Uuz5sCVzEVhwQ" name="dei:TradingSymbol" id="Tc_ZpOqEkVcSkCC7Eg7cb-NmA_6_1"><span style="line-height:1.2;">BATRK</span></ix:nonNumeric></p></div></div></td><td style="vertical-align:middle;width:39.66%;border-bottom:1px solid #000000;border-left:1px solid #000000;border-right:1px solid #000000;border-top:1px solid #000000;margin:0pt;padding:0pt 5.15pt 0pt 5.15pt;"><div style="height:14.4pt;overflow:hidden;overflow-wrap:break-word;position:relative;"><div style="position:absolute;top:50%;transform:translate(0,-50%);width:100%;"><p style="font-family:'Times New Roman';font-size:9pt;line-height:1.43;text-align:center;margin:0pt 0pt 6pt 0pt;"><ix:nonNumeric format="ixt-sec:exchnameen" contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertyBravesGroupCommonClassCMember_Fl2DCyEN9Uuz5sCVzEVhwQ" name="dei:SecurityExchangeName" id="Tc_yGcFo4HizkiZU79wBY08rg_6_2"><span style="line-height:1.2;">The Nasdaq Stock Market LLC</span></ix:nonNumeric></p></div></div></td></tr><tr style="height:14.4pt;"><td style="vertical-align:middle;width:39.92%;border-bottom:1px solid #000000;border-left:1px solid #000000;border-right:1px solid #000000;border-top:1px solid #000000;margin:0pt;padding:0pt 5.15pt 0pt 5.15pt;"><div style="height:14.4pt;overflow:hidden;overflow-wrap:break-word;position:relative;"><div style="position:absolute;top:50%;transform:translate(0,-50%);width:100%;"><p style="font-family:'Times New Roman';font-size:9pt;line-height:1.43;text-align:center;margin:0pt 0pt 6pt 0pt;"><ix:nonNumeric contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertyFormulaOneGroupCommonClassMember_Qku0i7SRpkKw2eLBgjmTUQ" name="dei:Security12bTitle" id="Tc_Dqqd6NMBkkmMXeqFIZzztA_7_0"><span style="line-height:1.2;">Series A Liberty Formula One Common Stock</span></ix:nonNumeric></p></div></div></td><td style="vertical-align:middle;width:20.41%;border-bottom:1px solid #000000;border-left:1px solid #000000;border-right:1px solid #000000;border-top:1px solid #000000;margin:0pt;padding:0pt 5.15pt 0pt 5.15pt;"><div style="height:14.4pt;overflow:hidden;overflow-wrap:break-word;position:relative;"><div style="position:absolute;top:50%;transform:translate(0,-50%);width:100%;"><p style="font-family:'Times New Roman';font-size:9pt;line-height:1.43;text-align:center;margin:0pt 0pt 6pt 0pt;"><ix:nonNumeric contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertyFormulaOneGroupCommonClassMember_Qku0i7SRpkKw2eLBgjmTUQ" name="dei:TradingSymbol" id="Tc_walnK5cQcUSXSZo0c4EcqA_7_1"><span style="line-height:1.2;">FWONA</span></ix:nonNumeric></p></div></div></td><td style="vertical-align:middle;width:39.66%;border-bottom:1px solid #000000;border-left:1px solid #000000;border-right:1px solid #000000;border-top:1px solid #000000;margin:0pt;padding:0pt 5.15pt 0pt 5.15pt;"><div style="height:14.4pt;overflow:hidden;overflow-wrap:break-word;position:relative;"><div style="position:absolute;top:50%;transform:translate(0,-50%);width:100%;"><p style="font-family:'Times New Roman';font-size:9pt;line-height:1.43;text-align:center;margin:0pt 0pt 6pt 0pt;"><ix:nonNumeric format="ixt-sec:exchnameen" contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertyFormulaOneGroupCommonClassMember_Qku0i7SRpkKw2eLBgjmTUQ" name="dei:SecurityExchangeName" id="Tc_F2wwLUlZAkSmm0uQZrcLvA_7_2"><span style="line-height:1.2;">The Nasdaq Stock Market LLC</span></ix:nonNumeric></p></div></div></td></tr><tr style="height:14.4pt;"><td style="vertical-align:middle;width:39.92%;border-bottom:1px solid #000000;border-left:1px solid #000000;border-right:1px solid #000000;border-top:1px solid #000000;margin:0pt;padding:0pt 5.15pt 0pt 5.15pt;"><div style="height:14.4pt;overflow:hidden;overflow-wrap:break-word;position:relative;"><div style="position:absolute;top:50%;transform:translate(0,-50%);width:100%;"><p style="font-family:'Times New Roman';font-size:9pt;line-height:1.43;text-align:center;margin:0pt 0pt 6pt 0pt;"><ix:nonNumeric contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertyFormulaOneGroupCommonClassCMember__WB6cnMrU0Cgg-JQk9l8tA" name="dei:Security12bTitle" id="Tc_myIN4vsr2kKIt4XeWscPGQ_8_0"><span style="line-height:1.2;">Series C Liberty Formula One Common Stock</span></ix:nonNumeric></p></div></div></td><td style="vertical-align:middle;width:20.41%;border-bottom:1px solid #000000;border-left:1px solid #000000;border-right:1px solid #000000;border-top:1px solid #000000;margin:0pt;padding:0pt 5.15pt 0pt 5.15pt;"><div style="height:14.4pt;overflow:hidden;overflow-wrap:break-word;position:relative;"><div style="position:absolute;top:50%;transform:translate(0,-50%);width:100%;"><p style="font-family:'Times New Roman';font-size:9pt;line-height:1.43;text-align:center;margin:0pt 0pt 6pt 0pt;"><ix:nonNumeric contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertyFormulaOneGroupCommonClassCMember__WB6cnMrU0Cgg-JQk9l8tA" name="dei:TradingSymbol" id="Tc_rgSEMIZV6UKSDKi2GKAr-g_8_1"><span style="line-height:1.2;">FWONK</span></ix:nonNumeric></p></div></div></td><td style="vertical-align:middle;width:39.66%;border-bottom:1px solid #000000;border-left:1px solid #000000;border-right:1px solid #000000;border-top:1px solid #000000;margin:0pt;padding:0pt 5.15pt 0pt 5.15pt;"><div style="height:14.4pt;overflow:hidden;overflow-wrap:break-word;position:relative;"><div style="position:absolute;top:50%;transform:translate(0,-50%);width:100%;"><p style="font-family:'Times New Roman';font-size:9pt;line-height:1.43;text-align:center;margin:0pt 0pt 6pt 0pt;"><ix:nonNumeric format="ixt-sec:exchnameen" contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertyFormulaOneGroupCommonClassCMember__WB6cnMrU0Cgg-JQk9l8tA" name="dei:SecurityExchangeName" id="Tc_cHmpG3UWtUaFbtx1WvGMSA_8_2"><span style="line-height:1.2;">The Nasdaq Stock Market LLC</span></ix:nonNumeric></p></div></div></td></tr></table><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.43;margin:0pt 0pt 6pt 0pt;"><span style="font-size:9pt;visibility:hidden;">&#8203;</span></p><p style="font-family:'Times New Roman';font-size:9pt;line-height:1.43;margin:0pt 0pt 6pt 0pt;"><span style="white-space:pre-wrap;">Indicate by check mark whether the registrant is an emerging growth company as defined in Rule 405 of the Securities Act of 1933 (&#167;230.405 of this chapter) or Rule 12b-2 of the Securities Exchange Act of 1934 (&#167;240.12b-2 of this chapter). Emerging growth company  </span><ix:nonNumeric format="ixt-sec:boolballotbox" contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA" name="dei:EntityEmergingGrowthCompany" id="Narr_nzq0eq_br0SUgGod7xseWg"><span style="font-family:'Segoe UI Symbol';">&#9744;</span></ix:nonNumeric></p><p style="font-family:'Times New Roman';font-size:9pt;line-height:1.43;margin:0pt;"><span style="white-space:pre-wrap;">If an emerging growth company, indicate by check mark if the registrant has elected not to use the extended transition period for complying with any new or revised financial accounting standards provided pursuant to Section 13(a) of the Exchange Act.  </span><span style="font-family:'Segoe UI Symbol';">&#9744;</span></p><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.43;margin:0pt;"><span style="font-size:1pt;visibility:hidden;">&#8203;</span></p><p style="display:none;line-height:0pt;margin:0pt;"><span style="font-family:'Times New Roman';font-size:0pt;visibility:hidden;">&#8203;</span></p></div></div>
<div style="background-color:#000000;clear:both;height:2pt;page-break-after:always;width:88.24%;border:0;margin:30pt 5.88% 30pt 5.88%;"></div><div style="max-width:100%;padding-left:11.76%;padding-right:11.76%;position:relative;"><div style="clear:both;max-width:100%;position:relative;"><p style="font-family:'Times New Roman';font-size:10pt;margin:0pt;"><b style="font-weight:bold;">Item 7.01. Regulation FD Disclosure.</b> </p><p style="font-family:'Times New Roman';font-size:10pt;margin:0pt;">&#160; </p><p style="font-family:'Times New Roman';font-size:10pt;text-align:justify;margin:0pt;">On November 26, 2019, Liberty Media Corporation announced that it has closed its previously announced private offering of $603.75 million aggregate original principal amount of its 2.75% Exchangeable Senior Debentures due 2049 (the &#8220;Debentures&#8221;), which includes $78.75 million aggregate principal amount of Debentures sold pursuant to the initial purchasers&#8217; option to purchase additional Debentures, which was exercised in full. </p><p style="font-family:'Times New Roman';font-size:10pt;text-align:justify;margin:0pt;">&#160; </p><a id="_Hlk24627962"></a><p style="font-family:'Times New Roman';font-size:10pt;text-align:justify;margin:0pt;">This Current Report on Form 8-K and the press release attached hereto as Exhibit 99.1 are being furnished to the Securities and Exchange Commission under Item 7.01 of Form 8-K in satisfaction of the public disclosure requirements of Regulation FD and shall not be deemed &quot;filed&quot; for any purpose. </p><p style="font-family:'Times New Roman';font-size:10pt;min-height:14.4pt;text-align:justify;margin:12pt 0pt 0pt 0pt;"><b style="font-weight:bold;">Item 9.01. Financial Statements and Exhibits.</b> </p><p style="font-family:'Times New Roman';font-size:10pt;min-height:14.4pt;text-align:justify;margin:0pt;">&#160; </p><p style="font-family:'Times New Roman';font-size:10pt;min-height:14.4pt;text-align:justify;margin:0pt;"><b style="font-weight:bold;">(d) Exhibits.</b> </p><p style="font-family:'Times New Roman';font-size:10pt;min-height:14.4pt;margin:0pt;">&#160; </p><table style="border-collapse:collapse;font-size:16pt;margin-left:auto;margin-right:auto;padding-left:3pt;padding-right:3pt;width:100%;"><tr style="height:1pt;"><td style="vertical-align:bottom;width:10.64%;margin:0pt;padding:0pt 3pt 0pt 3pt;"><div style="height:1pt;overflow:hidden;overflow-wrap:break-word;position:relative;"><div style="bottom:0pt;position:absolute;width:100%;"></div></div></td><td style="vertical-align:bottom;width:2.12%;margin:0pt;padding:0pt 3pt 0pt 3pt;"><div style="height:1pt;overflow:hidden;overflow-wrap:break-word;position:relative;"><div style="bottom:0pt;position:absolute;width:100%;"></div></div></td><td style="vertical-align:bottom;width:87.23%;margin:0pt;padding:0pt 3pt 0pt 3pt;"><div style="height:1pt;overflow:hidden;overflow-wrap:break-word;position:relative;"><div style="bottom:0pt;position:absolute;width:100%;"></div></div></td></tr><tr style="height:1pt;"><td style="vertical-align:bottom;width:10.64%;margin:0pt;padding:0pt 3pt 0pt 3pt;"><p style="font-family:'Times New Roman';font-size:1pt;line-height:1.27;margin:0pt;">&#160;</p></td><td style="vertical-align:bottom;width:2.12%;margin:0pt;padding:0pt 3pt 0pt 3pt;"><p style="font-family:'Times New Roman';font-size:1pt;line-height:1.27;margin:0pt;">&#160;</p></td><td style="vertical-align:bottom;width:87.23%;margin:0pt;padding:0pt 3pt 0pt 3pt;"><p style="font-family:'Times New Roman';font-size:1pt;line-height:1.27;margin:0pt;">&#160;</p></td></tr><tr><td style="vertical-align:bottom;width:10.64%;margin:0pt;padding:0pt 3pt 0pt 3pt;"><p style="font-family:'Times New Roman';font-size:9pt;line-height:1.27;margin:0pt;"><b style="font-weight:bold;">Exhibit No.</b></p></td><td style="vertical-align:bottom;width:2.12%;margin:0pt;padding:0pt 3pt 0pt 3pt;"><p style="font-family:'Times New Roman';font-size:9pt;line-height:1.27;margin:0pt;"><b style="font-weight:bold;">&#160;</b></p></td><td style="vertical-align:bottom;width:87.23%;margin:0pt;padding:0pt 3pt 0pt 3pt;"><p style="font-family:'Times New Roman';font-size:9pt;line-height:1.27;margin:0pt;"><b style="font-weight:bold;">Description </b></p></td></tr><tr><td style="vertical-align:bottom;width:10.64%;margin:0pt;padding:0pt 3pt 0pt 3pt;"><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.27;padding-left:7.2pt;text-indent:-7.2pt;margin:0pt;">99.1</p></td><td style="vertical-align:bottom;width:2.12%;margin:0pt;padding:0pt 3pt 0pt 3pt;"><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.27;margin:0pt;">&#160;</p></td><td style="vertical-align:bottom;width:87.23%;margin:0pt;padding:0pt 3pt 0pt 3pt;"><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.27;margin:0pt;"><a href="lmca-20191126ex991270c7c.htm"><span style="font-family:'Times New Roman';font-size:10pt;font-style:normal;font-weight:normal;line-height:1.27;text-align:left;">Press Release, dated November 26, 2019.</span></a>&#160;</p></td></tr><tr><td style="vertical-align:bottom;width:10.64%;margin:0pt;padding:0pt 3pt 0pt 3pt;"><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.27;padding-left:7.2pt;text-indent:-7.2pt;margin:0pt;">101.INS</p><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.27;padding-left:7.2pt;text-indent:-7.2pt;margin:0pt;"><span style="visibility:hidden;">&#8203;</span></p></td><td style="vertical-align:bottom;width:2.12%;margin:0pt;padding:0pt 3pt 0pt 3pt;"><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.27;margin:0pt;"><span style="visibility:hidden;">&#8203;</span></p></td><td style="vertical-align:bottom;width:87.23%;margin:0pt;padding:0pt 3pt 0pt 3pt;"><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.27;margin:0pt;">Inline XBRL Instance Document &#8211; the instance document does not appear in Interactive Data File because its XBRL tags are embedded within the Inline XBRL document.</p></td></tr><tr><td style="vertical-align:bottom;width:10.64%;margin:0pt;padding:0pt 3pt 0pt 3pt;"><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.27;padding-left:7.2pt;text-indent:-7.2pt;margin:0pt;">101.SCH</p></td><td style="vertical-align:bottom;width:2.12%;margin:0pt;padding:0pt 3pt 0pt 3pt;"><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.27;margin:0pt;"><span style="visibility:hidden;">&#8203;</span></p></td><td style="vertical-align:bottom;width:87.23%;margin:0pt;padding:0pt 3pt 0pt 3pt;"><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.27;margin:0pt;">Inline XBRL Taxonomy Extension Schema Document</p></td></tr><tr><td style="vertical-align:bottom;width:10.64%;margin:0pt;padding:0pt 3pt 0pt 3pt;"><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.27;padding-left:7.2pt;text-indent:-7.2pt;margin:0pt;">101.CAL</p></td><td style="vertical-align:bottom;width:2.12%;margin:0pt;padding:0pt 3pt 0pt 3pt;"><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.27;margin:0pt;"><span style="visibility:hidden;">&#8203;</span></p></td><td style="vertical-align:bottom;width:87.23%;margin:0pt;padding:0pt 3pt 0pt 3pt;"><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.27;margin:0pt;">Inline XBRL Taxonomy Extension Calculation Linkbase Document</p></td></tr><tr><td style="vertical-align:bottom;width:10.64%;margin:0pt;padding:0pt 3pt 0pt 3pt;"><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.27;padding-left:7.2pt;text-indent:-7.2pt;margin:0pt;">101.DEF</p></td><td style="vertical-align:bottom;width:2.12%;margin:0pt;padding:0pt 3pt 0pt 3pt;"><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.27;margin:0pt;"><span style="visibility:hidden;">&#8203;</span></p></td><td style="vertical-align:bottom;width:87.23%;margin:0pt;padding:0pt 3pt 0pt 3pt;"><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.27;margin:0pt;">Inline XBRL Taxonomy Extension Definition Linkbase Document</p></td></tr><tr><td style="vertical-align:bottom;width:10.64%;margin:0pt;padding:0pt 3pt 0pt 3pt;"><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.27;padding-left:7.2pt;text-indent:-7.2pt;margin:0pt;">101.LAB</p></td><td style="vertical-align:bottom;width:2.12%;margin:0pt;padding:0pt 3pt 0pt 3pt;"><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.27;margin:0pt;"><span style="visibility:hidden;">&#8203;</span></p></td><td style="vertical-align:bottom;width:87.23%;margin:0pt;padding:0pt 3pt 0pt 3pt;"><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.27;margin:0pt;">Inline XBRL Taxonomy Extension Label Linkbase Document</p></td></tr><tr><td style="vertical-align:bottom;width:10.64%;margin:0pt;padding:0pt 3pt 0pt 3pt;"><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.27;padding-left:7.2pt;text-indent:-7.2pt;margin:0pt;">101.PRE</p></td><td style="vertical-align:bottom;width:2.12%;margin:0pt;padding:0pt 3pt 0pt 3pt;"><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.27;margin:0pt;"><span style="visibility:hidden;">&#8203;</span></p></td><td style="vertical-align:bottom;width:87.23%;margin:0pt;padding:0pt 3pt 0pt 3pt;"><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.27;margin:0pt;">Inline XBRL Taxonomy Extension Presentation Linkbase Document</p></td></tr><tr><td style="vertical-align:bottom;width:10.64%;margin:0pt;padding:0pt 3pt 0pt 3pt;"><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.27;padding-left:7.2pt;text-indent:-7.2pt;margin:0pt;">104</p></td><td style="vertical-align:bottom;width:2.12%;margin:0pt;padding:0pt 3pt 0pt 3pt;"><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.27;margin:0pt;"><span style="visibility:hidden;">&#8203;</span></p></td><td style="vertical-align:bottom;width:87.23%;margin:0pt;padding:0pt 3pt 0pt 3pt;"><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.27;margin:0pt;">Cover Page Interactive Data File (formatted as Inline XBRL and contained in Exhibit 101)</p></td></tr></table><p style="font-family:'Times New Roman';font-size:10pt;margin:0pt;"><span style="visibility:hidden;">&#8203;</span></p><p style="font-family:'Times New Roman';font-size:10pt;margin:0pt;">&#160; </p><p style="font-family:'Times New Roman';font-size:7pt;margin:14pt 0pt 0pt 0pt;">&#160;</p><p style="font-family:'Times New Roman';font-size:10pt;margin:14pt 0pt 0pt 0pt;"><span style="visibility:hidden;">&#8203;</span></p></div><div style="clear:both;display:table;margin-bottom:30pt;min-height:36pt;width:100%;"><div style="display:table-cell;vertical-align:bottom;width:100%;"><p style="font-family:'Times New Roman';font-size:10pt;text-align:center;margin:0pt;">2</p><p style="font-family:'Times New Roman';font-size:10pt;margin:0pt;"><span style="visibility:hidden;">&#8203;</span></p></div></div></div>
<div style="background-color:#000000;clear:both;height:2pt;page-break-after:always;width:76.47%;border:0;margin:30pt 11.76% 30pt 11.76%;"></div><div style="max-width:100%;padding-left:11.76%;padding-right:11.76%;position:relative;"><div style="margin-top:21.6pt;width:100%;"><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.43;margin:0pt;"><span style="visibility:hidden;">&#8203;</span></p></div><div style="clear:both;max-width:100%;position:relative;"><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.43;text-align:center;margin:0pt;"><b style="font-weight:bold;">SIGNATURE</b></p><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.43;margin:0pt;"><span style="visibility:hidden;">&#8203;</span></p><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.43;text-indent:36pt;margin:0pt;">Pursuant to the requirements of the Securities Exchange Act of 1934, as amended, the registrant has duly caused this report to be signed on its behalf by the undersigned hereunto duly authorized.</p><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.43;margin:0pt;"><span style="visibility:hidden;">&#8203;</span></p><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.43;margin:0pt;">Date: November 26, 2019</p><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.43;margin:0pt;"><span style="visibility:hidden;">&#8203;</span></p><p style="font-family:'Times New Roman';font-size:10pt;line-height:1.43;margin:0pt;"><span style="visibility:hidden;">&#8203;</span></p><table style="border-collapse:collapse;font-size:16pt;padding-left:0pt;padding-right:0pt;width:100%;"><tr><td style="vertical-align:bottom;width:50.05%;margin:0pt;padding:0pt;"><p style="font-family:'Times New Roman';font-size:10pt;margin:0pt;"><span style="visibility:hidden;">&#8203;</span></p></td><td colspan="2" style="vertical-align:bottom;width:49.94%;margin:0pt;padding:0pt;"><p style="font-family:'Times New Roman';font-size:10pt;margin:0pt;">LIBERTY MEDIA CORPORATION</p></td></tr><tr><td style="vertical-align:bottom;width:50.05%;margin:0pt;padding:0pt;"><p style="font-family:'Times New Roman';font-size:10pt;margin:0pt;"><span style="visibility:hidden;">&#8203;</span></p></td><td style="vertical-align:bottom;width:4.58%;margin:0pt;padding:0pt;"><p style="font-family:'Times New Roman';font-size:10pt;margin:0pt;"><span style="visibility:hidden;">&#8203;</span></p></td><td style="vertical-align:bottom;width:45.36%;margin:0pt;padding:0pt;"><p style="font-family:'Times New Roman';font-size:10pt;margin:0pt;"><span style="text-decoration:underline;text-decoration-color:#000000;visibility:hidden;">&#8203;</span></p></td></tr><tr><td style="vertical-align:bottom;width:50.05%;margin:0pt;padding:0pt;"><p style="font-family:'Times New Roman';font-size:10pt;margin:0pt;"><span style="visibility:hidden;">&#8203;</span></p></td><td style="vertical-align:bottom;width:4.58%;margin:0pt;padding:0pt;"><p style="font-family:'Times New Roman';font-size:10pt;margin:0pt;"><span style="visibility:hidden;">&#8203;</span></p></td><td style="vertical-align:bottom;width:45.36%;margin:0pt;padding:0pt;"><p style="font-family:'Times New Roman';font-size:10pt;margin:0pt;"><span style="text-decoration:underline;text-decoration-color:#000000;visibility:hidden;">&#8203;</span></p></td></tr><tr><td style="vertical-align:bottom;width:50.05%;margin:0pt;padding:0pt;"><p style="font-family:'Times New Roman';font-size:10pt;margin:0pt;"><span style="visibility:hidden;">&#8203;</span></p></td><td style="vertical-align:bottom;width:4.58%;margin:0pt;padding:0pt;"><p style="font-family:'Times New Roman';font-size:10pt;margin:0pt;">By:</p></td><td style="vertical-align:bottom;width:45.36%;border-bottom:1px solid #000000;margin:0pt;padding:0pt;"><p style="font-family:'Times New Roman';font-size:10pt;margin:0pt;">/s/ Wade Haufschild</p></td></tr><tr><td style="vertical-align:bottom;width:50.05%;margin:0pt;padding:0pt;"><p style="font-family:'Times New Roman';font-size:10pt;margin:0pt;"><span style="visibility:hidden;">&#8203;</span></p></td><td style="vertical-align:bottom;width:4.58%;margin:0pt;padding:0pt;"><p style="font-family:'Times New Roman';font-size:10pt;margin:0pt;"><span style="visibility:hidden;">&#8203;</span></p></td><td style="vertical-align:bottom;width:45.36%;border-top:1px solid #000000;margin:0pt;padding:0pt;"><p style="font-family:'Times New Roman';font-size:10pt;margin:0pt;">Name: Wade Haufschild</p></td></tr><tr><td style="vertical-align:bottom;width:50.05%;margin:0pt;padding:0pt;"><p style="font-family:'Times New Roman';font-size:10pt;margin:0pt;"><span style="visibility:hidden;">&#8203;</span></p></td><td style="vertical-align:bottom;width:4.58%;margin:0pt;padding:0pt;"><p style="font-family:'Times New Roman';font-size:10pt;margin:0pt;"><span style="visibility:hidden;">&#8203;</span></p></td><td style="vertical-align:bottom;width:45.36%;margin:0pt;padding:0pt;"><p style="font-family:'Times New Roman';font-size:10pt;margin:0pt;"><span style="white-space:pre-wrap;">Title:  Vice President</span></p></td></tr></table><p style="font-family:'Times New Roman';font-size:10pt;margin:0pt;"><span style="visibility:hidden;">&#8203;</span></p><p style="display:none;line-height:0pt;margin:0pt;"><span style="font-family:'Times New Roman';font-size:0pt;visibility:hidden;">&#8203;</span></p></div><div style="clear:both;display:table;margin-bottom:30pt;min-height:36pt;width:100%;"><div style="display:table-cell;vertical-align:bottom;width:100%;"><p style="font-family:'Times New Roman';font-size:10pt;text-align:center;margin:0pt;">3</p><p style="font-family:'Times New Roman';font-size:10pt;text-align:center;margin:0pt;"><span style="visibility:hidden;">&#8203;</span></p></div></div></div>
<div style="background-color:#000000;clear:both;height:2pt;margin-left:11.76%;margin-right:11.76%;margin-top:30pt;page-break-after:avoid;width:76.47%;border:0;"></div></body></html>
</XBRL>
</TEXT>
</DOCUMENT>
<DOCUMENT>
<TYPE>EX-99.1
<SEQUENCE>2
<FILENAME>lmca-20191126ex991270c7c.htm
<DESCRIPTION>EX-99.1
<TEXT>
<!--HTML document created with Toppan Merrill Bridge  ********-->
<!--Created on: 11/26/2019 2:35:24 PM-->
<html>
	<head>
		<title>
			Exhibit 99.1
		</title>
	</head>
	<body><div style="margin-left:11.7647058823529%;margin-right:11.7647058823529%;"><div style="width:100%">

		<p style="margin:0pt;text-align:right;font-family:Times New Roman,Times,serif;font-size: 10pt;">

			<font style="display:inline;font-size:10pt;">Exhibit 99.1</font>

		</p>

</div></div><div style="margin-left:11.7647058823529%;margin-right:11.7647058823529%;">
		<p style="margin:0pt 0pt 12pt;border-bottom:1pt none #D9D9D9 ;text-align:justify;text-justify:inter-ideograph;font-family:Times New Roman,Times,serif;font-size: 10pt;">
			<font style="display:inline;font-size:10pt;">November 26, 2019</font>
		</p>
		<p style="margin:0pt 0pt 12pt;border-top:1pt none #D9D9D9 ;border-bottom:1pt none #D9D9D9 ;text-align:justify;text-justify:inter-ideograph;font-family:Times New Roman,Times,serif;font-size: 10pt;">
			<font style="display:inline;font-weight:bold;font-size:10pt;">Liberty Media Corporation Closes Private Offering of $603.75 Million of 2.75% Exchangeable Senior Debentures due 2049&nbsp;</font>
		</p>
		<p style="margin:0pt 0pt 12pt;border-top:1pt none #D9D9D9 ;border-bottom:1pt none #D9D9D9 ;text-align:justify;text-justify:inter-ideograph;font-family:Times New Roman,Times,serif;font-size: 10pt;">
			<font style="display:inline;font-size:10pt;">ENGLEWOOD, Colo.--(BUSINESS WIRE)-- November 26, 2019 Liberty Media Corporation (&#x201C;Liberty&#x201D;) (Nasdaq: LSXMA, LSXMB, LSXMK, BATRA, BATRK, FWONA, FWONK) announced today that it has closed its previously announced private offering of $603.75 million aggregate original principal amount of its 2.75% exchangeable senior debentures due 2049&nbsp;(the &#x201C;Debentures&#x201D;) exchangeable for Sirius XM Holdings Inc. (&#x201C;Sirius&nbsp;XM Holdings&#x201D;) common stock, which includes $78.75 million aggregate principal amount of Debentures sold pursuant to the initial purchasers&#x2019; option to purchase additional Debentures, which was exercised in full on November 25, 2019.&nbsp;</font>
		</p>
		<p style="margin:0pt 0pt 12pt;border-top:1pt none #D9D9D9 ;border-bottom:1pt none #D9D9D9 ;text-align:justify;text-justify:inter-ideograph;font-family:Times New Roman,Times,serif;font-size: 10pt;">
			<font style="display:inline;font-size:10pt;">Upon an exchange of Debentures, &nbsp;Liberty, at its option, may deliver Sirius XM Holdings common stock, the value thereof in cash or shares of Liberty&#x2019;s Series C Liberty SiriusXM Common Stock (&#x201C;LSXMK&#x201D;) or any combination of shares of Sirius&nbsp;XM Holdings common stock, cash and/or shares of LSXMK.&nbsp;&nbsp;Initially, 116.0227 shares of Sirius&nbsp;XM Holdings common stock are attributable to each $1,000 principal amount of Debentures, representing an initial exchange price of approximately $8.62 for each share of Sirius&nbsp;XM Holdings common stock.&nbsp;&nbsp;A total of approximately 70,048,705 shares of Sirius&nbsp;XM Holdings common stock are attributable to the Debentures.&nbsp;&nbsp;Interest is payable quarterly on March 1, June 1, September 1&nbsp;and December 1 of each year, commencing March 1, 2020.&nbsp;&nbsp;The Debentures may be redeemed by Liberty, in whole or in part, on or after December 1, 2024.&nbsp;&nbsp;Holders of the Debentures also have the right to require Liberty to purchase their Debentures on December 1, 2024.&nbsp;&nbsp;The redemption and purchase price will generally equal 100% of the adjusted principal amount of the Debentures plus accrued and unpaid interest to the redemption date, plus any final period distribution.</font>
		</p>
		<p style="margin:0pt 0pt 12pt;border-top:1pt none #D9D9D9 ;border-bottom:1pt none #D9D9D9 ;text-align:justify;text-justify:inter-ideograph;font-family:Times New Roman,Times,serif;font-size: 10pt;">
			<font style="display:inline;font-size:10pt;">Liberty expects to use the net proceeds of the offering for general corporate purposes, which may include&nbsp;(i) the repurchase of shares of Liberty SiriusXM common stock; (ii) possible acquisitions and investments; (iii) interest payments on the Debentures; and (iv) the repayment of borrowings outstanding under the margin loan secured by shares of Sirius XM Holdings. The Debentures, as well as the associated cash proceeds, will be attributed to the Liberty SiriusXM tracking stock group.</font>
		</p>
		<p style="margin:0pt 0pt 12pt;border-top:1pt none #D9D9D9 ;border-bottom:1pt none #D9D9D9 ;text-align:justify;text-justify:inter-ideograph;font-family:Times New Roman,Times,serif;font-size: 10pt;">
			<font style="display:inline;font-size:10pt;">The offering of the Debentures has&nbsp;not been registered under the Securities Act of 1933, as amended (the &#x201C;Securities Act&#x201D;), or any state securities laws and, unless so registered, may not be offered or sold in the United States except pursuant to an exemption from, or in a transaction not subject to, the registration requirements of the Securities Act and applicable state securities laws.&nbsp;&nbsp;The Debentures were offered by means of an offering memorandum solely to &#x201C;Qualified Institutional Buyers&#x201D; pursuant to, and as that term is defined in, Rule 144A of the Securities Act.</font>
		</p>
		<p style="margin:0pt 0pt 12pt;border-top:1pt none #D9D9D9 ;border-bottom:1pt none #D9D9D9 ;text-align:justify;text-justify:inter-ideograph;font-family:Times New Roman,Times,serif;font-size: 10pt;">
			<font style="display:inline;font-size:10pt;">This press release does not constitute an offer to sell or the solicitation of an offer to buy the Debentures nor shall there be any sale of Debentures in any state in which such offer, solicitation or sale would be unlawful prior to registration or qualification under the securities laws of such state.</font>
		</p>
		<p style="margin:0pt 0pt 12pt;border-top:1pt none #D9D9D9 ;border-bottom:1pt none #D9D9D9 ;text-align:justify;text-justify:inter-ideograph;font-family:Times New Roman,Times,serif;font-size: 10pt;">
			<font style="display:inline;font-size:10pt;text-decoration:underline;">Forward-Looking Statements</font>
		</p>
		<p style="margin:0pt 0pt 12pt;border-top:1pt none #D9D9D9 ;text-align:justify;text-justify:inter-ideograph;font-family:Times New Roman,Times,serif;font-size: 10pt;">
			<font style="display:inline;font-size:10pt;">This press release includes certain forward-looking statements within the meaning of the Private Securities Litigation Reform Act of 1995, including statements relating to the offering of Debentures and the use of proceeds therefrom, and the concurrent repurchase by Sirius XM Holdings of its common stock.&nbsp;&nbsp;These forward-looking statements involve many risks and uncertainties that could cause actual results to differ materially from those expressed or implied by such statements, including, without limitation, general market conditions.&nbsp;&nbsp;These forward-looking statements speak only as of the date of this press release, and Liberty expressly disclaims any obligation or undertaking to disseminate any updates or revisions to any forward-looking statement contained herein to reflect any change in Liberty&#x2019;s expectations with regard thereto or any change in events, conditions or circumstances on which any such statement is based.&nbsp;&nbsp;Please refer to the publicly filed documents of Liberty, including its most recent Annual Report on Form 10-K and Quarterly Reports on Form 10-Q, for risks and uncertainties related to Liberty&#x2019;s business which may affect the statements made in this press release.</font>
		</p>
		<p style="margin:0pt;font-family:Times New Roman,Times,serif;font-size: 10pt;">
			<font style="display:inline;font-size:10pt;text-decoration:underline;"></font>
		</p>
		<p style="margin:0pt;font-family:Times New Roman,Times,serif;font-size: 12pt;">
			<font style="display:inline;"></font></p></div><div style="margin-left:11.7647058823529%;margin-right:11.7647058823529%;">
		<p><font size="1"> </font></p></div><div style="margin-left:11.7647058823529%;margin-right:11.7647058823529%;page-break-after:always;"><div style="background-color:#000000;clear:both;height:2pt;border:0;margin:30pt 0pt 30pt 0pt;"></div></div><div style="margin-left:11.7647058823529%;margin-right:11.7647058823529%;"></div><div style="margin-left:11.7647058823529%;margin-right:11.7647058823529%;"><p style="margin:0pt;font-family:Times New Roman,Times,serif;font-size: 12pt;"><font style="display:inline;"></font>
		</p>
		<p style="margin:0pt 0pt 12pt;border-bottom:1pt none #D9D9D9 ;text-align:justify;text-justify:inter-ideograph;font-family:Times New Roman,Times,serif;font-size: 10pt;">
			<font style="display:inline;font-size:10pt;text-decoration:underline;">About Liberty Media Corporation</font>
		</p>
		<p style="margin:0pt 0pt 12pt;border-top:1pt none #D9D9D9 ;border-bottom:1pt none #D9D9D9 ;text-align:justify;text-justify:inter-ideograph;font-family:Times New Roman,Times,serif;font-size: 10pt;">
			<font style="display:inline;font-size:10pt;">Liberty Media Corporation operates and owns interests in a broad range of media, communications and entertainment businesses.&nbsp;&nbsp;Those businesses are attributed to three tracking stock groups: the Liberty SiriusXM Group, the Braves Group and the Formula One Group.&nbsp;&nbsp;The businesses and assets attributed to the Liberty SiriusXM Group (Nasdaq: LSXMA, LSXMB, LSXMK) include Liberty Media Corporation&#x2019;s interest in Sirius&nbsp;XM Holdings.&nbsp;&nbsp;The businesses and assets attributed to the Braves Group (Nasdaq: BATRA, BATRK) include Liberty Media Corporation&#x2019;s subsidiary Braves Holdings, LLC.&nbsp;&nbsp;The businesses and assets attributed to the Formula One Group (Nasdaq: FWONA, FWONK) consist of all of Liberty Media Corporation&#x2019;s&nbsp;businesses and assets other than those attributed to the Liberty SiriusXM Group and the Braves Group, including its subsidiary Formula 1, its interest in Live Nation Entertainment and minority equity investment in AT&amp;T Inc.</font>
		</p>
		<p style="margin:0pt 0pt 12pt;text-align:left;border-top:1pt none #D9D9D9 ;border-bottom:1pt none #D9D9D9 ;text-justify:inter-ideograph;font-family:Times New Roman,Times,serif;font-size: 10pt;">
			<font style="display:inline;font-weight:bold;font-size:10pt;">Liberty Media Corporation <br></font><font style="display:inline;font-size:10pt;">Courtnee Chun, 720-875-5420 </font>
		</p>
		<p style="margin:0pt 0pt 12pt;text-align:left;border-top:1pt none #D9D9D9 ;border-bottom:1pt none #D9D9D9 ;text-justify:inter-ideograph;font-family:Times New Roman,Times,serif;font-size: 10pt;">
			<font style="display:inline;font-size:10pt;">Source: Liberty Media Corporation </font>
		</p>
		<p style="margin:0pt 0pt 12pt;border-top:1pt none #D9D9D9 ;text-align:justify;text-justify:inter-ideograph;font-family:Times New Roman,Times,serif;font-size: 10pt;">
			<font style="display:inline;font-size:10pt;">&nbsp;</font>
		</p></div><div style="margin-left:11.7647058823529%;margin-right:11.7647058823529%;">
		<p><font size="1"> </font></p><div style="width:100%">

		<p style="margin:0pt;text-align:center;font-family:Times New Roman,Times,serif;font-size: 10pt;">

			<font style="display:inline;font-size:10pt;">2</font>

		</p>

</div></div><div style="margin-left:11.7647058823529%;margin-right:11.7647058823529%;page-break-after:avoid;"><div style="background-color:#000000;clear:both;height:2pt;border:0;margin:30pt 0pt 30pt 0pt;"></div></div>
	</body>
</html>
</TEXT>
</DOCUMENT>
<DOCUMENT>
<TYPE>EX-101.SCH
<SEQUENCE>3
<FILENAME>lmca-20191126.xsd
<DESCRIPTION>EX-101.SCH
<TEXT>
<XBRL>
<?xml version="1.0" encoding="us-ascii"?>
<!--XBRL document created with Toppan Merrill Bridge  9.4.7193.41938 -->
<!--Based on XBRL 2.1-->
<!--Created on: 11/26/2019 9:35:32 PM-->
<!--Modified on: 11/26/2019 9:35:32 PM-->
<xsd:schema targetNamespace="http://www.libertymedia.com/20191126" attributeFormDefault="unqualified" elementFormDefault="qualified" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:invest="http://xbrl.sec.gov/invest/2013-01-31" xmlns:dei="http://xbrl.sec.gov/dei/2019-01-31" xmlns:us-gaap="http://fasb.org/us-gaap/2019-01-31" xmlns:srt-types="http://fasb.org/srt-types/2019-01-31" xmlns:us-roles="http://fasb.org/us-roles/2019-01-31" xmlns:ref="http://www.xbrl.org/2006/ref" xmlns:xbrldt="http://xbrl.org/2005/xbrldt" xmlns:nonnum="http://www.xbrl.org/dtr/type/non-numeric" xmlns:currency="http://xbrl.sec.gov/currency/2017-01-31" xmlns:xl="http://www.xbrl.org/2003/XLink" xmlns:naics="http://xbrl.sec.gov/naics/2017-01-31" xmlns:srt-roles="http://fasb.org/srt-roles/2019-01-31" xmlns:country="http://xbrl.sec.gov/country/2017-01-31" xmlns:utr="http://www.xbrl.org/2009/utr" xmlns:srt="http://fasb.org/srt/2019-01-31" xmlns:link="http://www.xbrl.org/2003/linkbase" xmlns:exch="http://xbrl.sec.gov/exch/2019-01-31" xmlns:num="http://www.xbrl.org/dtr/type/numeric" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:sic="http://xbrl.sec.gov/sic/2011-01-31" xmlns:us-types="http://fasb.org/us-types/2019-01-31" xmlns:negated="http://www.xbrl.org/2009/role/negated" xmlns:stpr="http://xbrl.sec.gov/stpr/2018-01-31" xmlns:attributeFormDefault="unqualified" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xbrli="http://www.xbrl.org/2003/instance" xmlns:lmca="http://www.libertymedia.com/20191126">
  <xsd:annotation>
    <xsd:appinfo>
      <link:roleType roleURI="http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation" id="DocumentDocumentAndEntityInformation">
        <link:definition>00090 - Document - Document and Entity Information</link:definition>
        <link:usedOn>link:presentationLink</link:usedOn>
        <link:usedOn>link:calculationLink</link:usedOn>
        <link:usedOn>link:definitionLink</link:usedOn>
      </link:roleType>
      <link:linkbaseRef xlink:type="simple" xlink:href="lmca-20191126_pre.xml" xlink:role="http://www.xbrl.org/2003/role/presentationLinkbaseRef" xlink:arcrole="http://www.w3.org/1999/xlink/properties/linkbase" />
      <link:linkbaseRef xlink:type="simple" xlink:href="lmca-20191126_def.xml" xlink:role="http://www.xbrl.org/2003/role/definitionLinkbaseRef" xlink:arcrole="http://www.w3.org/1999/xlink/properties/linkbase" />
      <link:linkbaseRef xlink:type="simple" xlink:href="lmca-20191126_lab.xml" xlink:role="http://www.xbrl.org/2003/role/labelLinkbaseRef" xlink:arcrole="http://www.w3.org/1999/xlink/properties/linkbase" />
    </xsd:appinfo>
  </xsd:annotation>
  <xsd:import namespace="http://www.xbrl.org/2003/instance" schemaLocation="http://www.xbrl.org/2003/xbrl-instance-2003-12-31.xsd" />
  <xsd:import namespace="http://fasb.org/us-gaap/2019-01-31" schemaLocation="http://xbrl.fasb.org/us-gaap/2019/elts/us-gaap-2019-01-31.xsd" />
  <xsd:import namespace="http://xbrl.org/2005/xbrldt" schemaLocation="http://www.xbrl.org/2005/xbrldt-2005.xsd" />
  <xsd:import namespace="http://xbrl.sec.gov/dei/2019-01-31" schemaLocation="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd" />
  <xsd:import namespace="http://www.xbrl.org/dtr/type/non-numeric" schemaLocation="http://www.xbrl.org/dtr/type/nonNumeric-2009-12-16.xsd" />
  <xsd:element name="DocumentAndEntityInformationAbstract" id="lmca_DocumentAndEntityInformationAbstract" type="xbrli:stringItemType" substitutionGroup="xbrli:item" abstract="true" nillable="true" xbrli:periodType="duration" />
  <element id="lmca_LibertySiriusXmGroupCommonClassMember" name="LibertySiriusXmGroupCommonClassMember" nillable="true" type="nonnum:domainItemType" substitutionGroup="xbrli:item" abstract="true" xbrli:periodType="duration" xmlns="http://www.w3.org/2001/XMLSchema" />
  <element id="lmca_LibertySiriusXmGroupCommonClassBMember" name="LibertySiriusXmGroupCommonClassBMember" nillable="true" type="nonnum:domainItemType" substitutionGroup="xbrli:item" abstract="true" xbrli:periodType="duration" xmlns="http://www.w3.org/2001/XMLSchema" />
  <element id="lmca_LibertyBravesGroupCommonClassMember" name="LibertyBravesGroupCommonClassMember" nillable="true" type="nonnum:domainItemType" substitutionGroup="xbrli:item" abstract="true" xbrli:periodType="duration" xmlns="http://www.w3.org/2001/XMLSchema" />
  <element id="lmca_LibertyBravesGroupCommonClassCMember" name="LibertyBravesGroupCommonClassCMember" nillable="true" type="nonnum:domainItemType" substitutionGroup="xbrli:item" abstract="true" xbrli:periodType="duration" xmlns="http://www.w3.org/2001/XMLSchema" />
  <element id="lmca_LibertyFormulaOneGroupCommonClassMember" name="LibertyFormulaOneGroupCommonClassMember" nillable="true" type="nonnum:domainItemType" substitutionGroup="xbrli:item" abstract="true" xbrli:periodType="duration" xmlns="http://www.w3.org/2001/XMLSchema" />
  <element id="lmca_LibertyFormulaOneGroupCommonClassCMember" name="LibertyFormulaOneGroupCommonClassCMember" nillable="true" type="nonnum:domainItemType" substitutionGroup="xbrli:item" abstract="true" xbrli:periodType="duration" xmlns="http://www.w3.org/2001/XMLSchema" />
  <element id="lmca_LibertySiriusXmGroupCommonClassCMember" name="LibertySiriusXmGroupCommonClassCMember" nillable="true" type="nonnum:domainItemType" substitutionGroup="xbrli:item" abstract="true" xbrli:periodType="duration" xmlns="http://www.w3.org/2001/XMLSchema" />
</xsd:schema>
</XBRL>
</TEXT>
</DOCUMENT>
<DOCUMENT>
<TYPE>EX-101.DEF
<SEQUENCE>4
<FILENAME>lmca-20191126_def.xml
<DESCRIPTION>EX-101.DEF
<TEXT>
<XBRL>
<?xml version="1.0" encoding="us-ascii"?>
<!--XBRL document created with Toppan Merrill Bridge  9.4.7193.41938 -->
<!--Based on XBRL 2.1-->
<!--Created on: 11/26/2019 9:35:32 PM-->
<!--Modified on: 11/26/2019 9:35:32 PM-->
<link:linkbase xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.xbrl.org/2003/linkbase http://www.xbrl.org/2003/xbrl-linkbase-2003-12-31.xsd" xmlns:link="http://www.xbrl.org/2003/linkbase" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xbrli="http://www.xbrl.org/2003/instance" xmlns:xbrldt="http://xbrl.org/2005/xbrldt">
  <roleRef xlink:type="simple" xlink:href="lmca-20191126.xsd#DocumentDocumentAndEntityInformation" roleURI="http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation" xmlns="http://www.xbrl.org/2003/linkbase" />
  <link:arcroleRef arcroleURI="http://xbrl.org/int/dim/arcrole/all" xlink:type="simple" xlink:href="http://www.xbrl.org/2005/xbrldt-2005.xsd#all" />
  <link:arcroleRef arcroleURI="http://xbrl.org/int/dim/arcrole/dimension-domain" xlink:type="simple" xlink:href="http://www.xbrl.org/2005/xbrldt-2005.xsd#dimension-domain" />
  <link:arcroleRef arcroleURI="http://xbrl.org/int/dim/arcrole/domain-member" xlink:type="simple" xlink:href="http://www.xbrl.org/2005/xbrldt-2005.xsd#domain-member" />
  <link:arcroleRef arcroleURI="http://xbrl.org/int/dim/arcrole/hypercube-dimension" xlink:type="simple" xlink:href="http://www.xbrl.org/2005/xbrldt-2005.xsd#hypercube-dimension" />
  <link:arcroleRef arcroleURI="http://xbrl.org/int/dim/arcrole/dimension-default" xlink:type="simple" xlink:href="http://www.xbrl.org/2005/xbrldt-2005.xsd#dimension-default" />
  <definitionLink xlink:role="http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation" xlink:type="extended" xlink:title="00090 - Document - Document and Entity Information" xmlns="http://www.xbrl.org/2003/linkbase">
    <loc xlink:type="locator" xlink:href="http://xbrl.fasb.org/us-gaap/2019/elts/us-gaap-2019-01-31.xsd#us-gaap_StatementTable" xlink:label="us-gaap_StatementTable" />
    <loc xlink:type="locator" xlink:href="http://xbrl.fasb.org/us-gaap/2019/elts/us-gaap-2019-01-31.xsd#us-gaap_StatementClassOfStockAxis" xlink:label="us-gaap_StatementClassOfStockAxis" />
    <definitionArc xlink:type="arc" xlink:arcrole="http://xbrl.org/int/dim/arcrole/hypercube-dimension" order="1" xlink:from="us-gaap_StatementTable" xlink:to="us-gaap_StatementClassOfStockAxis" use="optional" />
    <loc xlink:type="locator" xlink:href="http://xbrl.fasb.org/us-gaap/2019/elts/us-gaap-2019-01-31.xsd#us-gaap_ClassOfStockDomain" xlink:label="us-gaap_ClassOfStockDomain" />
    <definitionArc xlink:type="arc" xlink:arcrole="http://xbrl.org/int/dim/arcrole/dimension-domain" order="1" xlink:from="us-gaap_StatementClassOfStockAxis" xlink:to="us-gaap_ClassOfStockDomain" use="optional" />
    <loc xlink:type="locator" xlink:href="http://xbrl.fasb.org/us-gaap/2019/elts/us-gaap-2019-01-31.xsd#us-gaap_ClassOfStockDomain" xlink:label="us-gaap_ClassOfStockDomain_637104009323477796" />
    <definitionArc xlink:type="arc" xlink:arcrole="http://xbrl.org/int/dim/arcrole/dimension-default" order="1" xlink:from="us-gaap_StatementClassOfStockAxis" xlink:to="us-gaap_ClassOfStockDomain_637104009323477796" use="optional" />
    <loc xlink:type="locator" xlink:href="lmca-20191126.xsd#lmca_LibertySiriusXmGroupCommonClassMember" xlink:label="lmca_LibertySiriusXmGroupCommonClassMember" />
    <definitionArc xlink:type="arc" xlink:arcrole="http://xbrl.org/int/dim/arcrole/domain-member" order="1" xlink:from="us-gaap_ClassOfStockDomain" xlink:to="lmca_LibertySiriusXmGroupCommonClassMember" priority="1" use="optional" />
    <loc xlink:type="locator" xlink:href="lmca-20191126.xsd#lmca_LibertySiriusXmGroupCommonClassBMember" xlink:label="lmca_LibertySiriusXmGroupCommonClassBMember" />
    <definitionArc xlink:type="arc" xlink:arcrole="http://xbrl.org/int/dim/arcrole/domain-member" order="2" xlink:from="us-gaap_ClassOfStockDomain" xlink:to="lmca_LibertySiriusXmGroupCommonClassBMember" priority="1" use="optional" />
    <loc xlink:type="locator" xlink:href="lmca-20191126.xsd#lmca_LibertySiriusXmGroupCommonClassCMember" xlink:label="lmca_LibertySiriusXmGroupCommonClassCMember" />
    <definitionArc xlink:type="arc" xlink:arcrole="http://xbrl.org/int/dim/arcrole/domain-member" order="3" xlink:from="us-gaap_ClassOfStockDomain" xlink:to="lmca_LibertySiriusXmGroupCommonClassCMember" priority="1" use="optional" />
    <loc xlink:type="locator" xlink:href="lmca-20191126.xsd#lmca_LibertyBravesGroupCommonClassMember" xlink:label="lmca_LibertyBravesGroupCommonClassMember" />
    <definitionArc xlink:type="arc" xlink:arcrole="http://xbrl.org/int/dim/arcrole/domain-member" order="4" xlink:from="us-gaap_ClassOfStockDomain" xlink:to="lmca_LibertyBravesGroupCommonClassMember" priority="1" use="optional" />
    <loc xlink:type="locator" xlink:href="lmca-20191126.xsd#lmca_LibertyBravesGroupCommonClassCMember" xlink:label="lmca_LibertyBravesGroupCommonClassCMember" />
    <definitionArc xlink:type="arc" xlink:arcrole="http://xbrl.org/int/dim/arcrole/domain-member" order="5" xlink:from="us-gaap_ClassOfStockDomain" xlink:to="lmca_LibertyBravesGroupCommonClassCMember" priority="1" use="optional" />
    <loc xlink:type="locator" xlink:href="lmca-20191126.xsd#lmca_LibertyFormulaOneGroupCommonClassMember" xlink:label="lmca_LibertyFormulaOneGroupCommonClassMember" />
    <definitionArc xlink:type="arc" xlink:arcrole="http://xbrl.org/int/dim/arcrole/domain-member" order="6" xlink:from="us-gaap_ClassOfStockDomain" xlink:to="lmca_LibertyFormulaOneGroupCommonClassMember" priority="1" use="optional" />
    <loc xlink:type="locator" xlink:href="lmca-20191126.xsd#lmca_LibertyFormulaOneGroupCommonClassCMember" xlink:label="lmca_LibertyFormulaOneGroupCommonClassCMember" />
    <definitionArc xlink:type="arc" xlink:arcrole="http://xbrl.org/int/dim/arcrole/domain-member" order="7" xlink:from="us-gaap_ClassOfStockDomain" xlink:to="lmca_LibertyFormulaOneGroupCommonClassCMember" priority="1" use="optional" />
    <loc xlink:type="locator" xlink:href="http://xbrl.fasb.org/us-gaap/2019/elts/us-gaap-2019-01-31.xsd#us-gaap_StatementLineItems" xlink:label="us-gaap_StatementLineItems" />
    <definitionArc xlink:type="arc" xlink:arcrole="http://xbrl.org/int/dim/arcrole/all" order="1" xlink:from="us-gaap_StatementLineItems" xlink:to="us-gaap_StatementTable" use="optional" xbrldt:contextElement="segment" xbrldt:closed="true" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_DocumentType" xlink:label="dei_DocumentType" />
    <definitionArc xlink:type="arc" xlink:arcrole="http://xbrl.org/int/dim/arcrole/domain-member" order="2" xlink:from="us-gaap_StatementLineItems" xlink:to="dei_DocumentType" priority="1" use="optional" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_DocumentPeriodEndDate" xlink:label="dei_DocumentPeriodEndDate" />
    <definitionArc xlink:type="arc" xlink:arcrole="http://xbrl.org/int/dim/arcrole/domain-member" order="3" xlink:from="us-gaap_StatementLineItems" xlink:to="dei_DocumentPeriodEndDate" priority="1" use="optional" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_EntityRegistrantName" xlink:label="dei_EntityRegistrantName" />
    <definitionArc xlink:type="arc" xlink:arcrole="http://xbrl.org/int/dim/arcrole/domain-member" order="4" xlink:from="us-gaap_StatementLineItems" xlink:to="dei_EntityRegistrantName" priority="1" use="optional" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_EntityIncorporationStateCountryCode" xlink:label="dei_EntityIncorporationStateCountryCode" />
    <definitionArc xlink:type="arc" xlink:arcrole="http://xbrl.org/int/dim/arcrole/domain-member" order="5" xlink:from="us-gaap_StatementLineItems" xlink:to="dei_EntityIncorporationStateCountryCode" priority="1" use="optional" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_EntityFileNumber" xlink:label="dei_EntityFileNumber" />
    <definitionArc xlink:type="arc" xlink:arcrole="http://xbrl.org/int/dim/arcrole/domain-member" order="6" xlink:from="us-gaap_StatementLineItems" xlink:to="dei_EntityFileNumber" priority="1" use="optional" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_EntityTaxIdentificationNumber" xlink:label="dei_EntityTaxIdentificationNumber" />
    <definitionArc xlink:type="arc" xlink:arcrole="http://xbrl.org/int/dim/arcrole/domain-member" order="7" xlink:from="us-gaap_StatementLineItems" xlink:to="dei_EntityTaxIdentificationNumber" priority="1" use="optional" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_EntityAddressAddressLine1" xlink:label="dei_EntityAddressAddressLine1" />
    <definitionArc xlink:type="arc" xlink:arcrole="http://xbrl.org/int/dim/arcrole/domain-member" order="8" xlink:from="us-gaap_StatementLineItems" xlink:to="dei_EntityAddressAddressLine1" priority="1" use="optional" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_EntityAddressCityOrTown" xlink:label="dei_EntityAddressCityOrTown" />
    <definitionArc xlink:type="arc" xlink:arcrole="http://xbrl.org/int/dim/arcrole/domain-member" order="9" xlink:from="us-gaap_StatementLineItems" xlink:to="dei_EntityAddressCityOrTown" priority="1" use="optional" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_EntityAddressStateOrProvince" xlink:label="dei_EntityAddressStateOrProvince" />
    <definitionArc xlink:type="arc" xlink:arcrole="http://xbrl.org/int/dim/arcrole/domain-member" order="10" xlink:from="us-gaap_StatementLineItems" xlink:to="dei_EntityAddressStateOrProvince" priority="1" use="optional" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_EntityAddressPostalZipCode" xlink:label="dei_EntityAddressPostalZipCode" />
    <definitionArc xlink:type="arc" xlink:arcrole="http://xbrl.org/int/dim/arcrole/domain-member" order="11" xlink:from="us-gaap_StatementLineItems" xlink:to="dei_EntityAddressPostalZipCode" priority="1" use="optional" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_CityAreaCode" xlink:label="dei_CityAreaCode" />
    <definitionArc xlink:type="arc" xlink:arcrole="http://xbrl.org/int/dim/arcrole/domain-member" order="12" xlink:from="us-gaap_StatementLineItems" xlink:to="dei_CityAreaCode" priority="1" use="optional" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_LocalPhoneNumber" xlink:label="dei_LocalPhoneNumber" />
    <definitionArc xlink:type="arc" xlink:arcrole="http://xbrl.org/int/dim/arcrole/domain-member" order="13" xlink:from="us-gaap_StatementLineItems" xlink:to="dei_LocalPhoneNumber" priority="1" use="optional" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_WrittenCommunications" xlink:label="dei_WrittenCommunications" />
    <definitionArc xlink:type="arc" xlink:arcrole="http://xbrl.org/int/dim/arcrole/domain-member" order="14" xlink:from="us-gaap_StatementLineItems" xlink:to="dei_WrittenCommunications" priority="1" use="optional" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_SolicitingMaterial" xlink:label="dei_SolicitingMaterial" />
    <definitionArc xlink:type="arc" xlink:arcrole="http://xbrl.org/int/dim/arcrole/domain-member" order="15" xlink:from="us-gaap_StatementLineItems" xlink:to="dei_SolicitingMaterial" priority="1" use="optional" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_PreCommencementTenderOffer" xlink:label="dei_PreCommencementTenderOffer" />
    <definitionArc xlink:type="arc" xlink:arcrole="http://xbrl.org/int/dim/arcrole/domain-member" order="16" xlink:from="us-gaap_StatementLineItems" xlink:to="dei_PreCommencementTenderOffer" priority="1" use="optional" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_PreCommencementIssuerTenderOffer" xlink:label="dei_PreCommencementIssuerTenderOffer" />
    <definitionArc xlink:type="arc" xlink:arcrole="http://xbrl.org/int/dim/arcrole/domain-member" order="17" xlink:from="us-gaap_StatementLineItems" xlink:to="dei_PreCommencementIssuerTenderOffer" priority="1" use="optional" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_Security12bTitle" xlink:label="dei_Security12bTitle" />
    <definitionArc xlink:type="arc" xlink:arcrole="http://xbrl.org/int/dim/arcrole/domain-member" order="18" xlink:from="us-gaap_StatementLineItems" xlink:to="dei_Security12bTitle" priority="1" use="optional" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_TradingSymbol" xlink:label="dei_TradingSymbol" />
    <definitionArc xlink:type="arc" xlink:arcrole="http://xbrl.org/int/dim/arcrole/domain-member" order="19" xlink:from="us-gaap_StatementLineItems" xlink:to="dei_TradingSymbol" priority="1" use="optional" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_SecurityExchangeName" xlink:label="dei_SecurityExchangeName" />
    <definitionArc xlink:type="arc" xlink:arcrole="http://xbrl.org/int/dim/arcrole/domain-member" order="20" xlink:from="us-gaap_StatementLineItems" xlink:to="dei_SecurityExchangeName" priority="1" use="optional" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_EntityEmergingGrowthCompany" xlink:label="dei_EntityEmergingGrowthCompany" />
    <definitionArc xlink:type="arc" xlink:arcrole="http://xbrl.org/int/dim/arcrole/domain-member" order="21" xlink:from="us-gaap_StatementLineItems" xlink:to="dei_EntityEmergingGrowthCompany" priority="1" use="optional" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_EntityCentralIndexKey" xlink:label="dei_EntityCentralIndexKey" />
    <definitionArc xlink:type="arc" xlink:arcrole="http://xbrl.org/int/dim/arcrole/domain-member" order="22" xlink:from="us-gaap_StatementLineItems" xlink:to="dei_EntityCentralIndexKey" priority="1" use="optional" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_AmendmentFlag" xlink:label="dei_AmendmentFlag" />
    <definitionArc xlink:type="arc" xlink:arcrole="http://xbrl.org/int/dim/arcrole/domain-member" order="23" xlink:from="us-gaap_StatementLineItems" xlink:to="dei_AmendmentFlag" priority="1" use="optional" />
  </definitionLink>
</link:linkbase>
</XBRL>
</TEXT>
</DOCUMENT>
<DOCUMENT>
<TYPE>EX-101.LAB
<SEQUENCE>5
<FILENAME>lmca-20191126_lab.xml
<DESCRIPTION>EX-101.LAB
<TEXT>
<XBRL>
<?xml version="1.0" encoding="us-ascii"?>
<!--XBRL document created with Toppan Merrill Bridge  9.4.7193.41938 -->
<!--Based on XBRL 2.1-->
<!--Created on: 11/26/2019 9:35:32 PM-->
<!--Modified on: 11/26/2019 9:35:32 PM-->
<link:linkbase xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.xbrl.org/2003/linkbase http://www.xbrl.org/2003/xbrl-linkbase-2003-12-31.xsd" xmlns:link="http://www.xbrl.org/2003/linkbase" xmlns:xbrldt="http://xbrl.org/2005/xbrldt" xmlns:xlink="http://www.w3.org/1999/xlink">
  <link:labelLink xlink:type="extended" xlink:role="http://www.xbrl.org/2003/role/link">
    <loc xlink:type="locator" xlink:href="lmca-20191126.xsd#lmca_DocumentAndEntityInformationAbstract" xlink:label="lmca_DocumentAndEntityInformationAbstract" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="lmca_DocumentAndEntityInformationAbstract" xlink:to="lmca_DocumentAndEntityInformationAbstract_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="lmca_DocumentAndEntityInformationAbstract_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Document and Entity Information [Abstract]</label>
    <loc xlink:type="locator" xlink:href="http://xbrl.fasb.org/us-gaap/2019/elts/us-gaap-2019-01-31.xsd#us-gaap_StatementTable" xlink:label="us-gaap_StatementTable" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="us-gaap_StatementTable" xlink:to="us-gaap_StatementTable_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="us-gaap_StatementTable_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Statement [Table]</label>
    <loc xlink:type="locator" xlink:href="http://xbrl.fasb.org/us-gaap/2019/elts/us-gaap-2019-01-31.xsd#us-gaap_StatementLineItems" xlink:label="us-gaap_StatementLineItems" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="us-gaap_StatementLineItems" xlink:to="us-gaap_StatementLineItems_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="us-gaap_StatementLineItems_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Statement [Line Items]</label>
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_DocumentType" xlink:label="dei_DocumentType" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="dei_DocumentType" xlink:to="dei_DocumentType_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="dei_DocumentType_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Document Type</label>
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_DocumentPeriodEndDate" xlink:label="dei_DocumentPeriodEndDate" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="dei_DocumentPeriodEndDate" xlink:to="dei_DocumentPeriodEndDate_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="dei_DocumentPeriodEndDate_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Document Period End Date</label>
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_EntityRegistrantName" xlink:label="dei_EntityRegistrantName" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="dei_EntityRegistrantName" xlink:to="dei_EntityRegistrantName_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="dei_EntityRegistrantName_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Entity Registrant Name</label>
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_EntityIncorporationStateCountryCode" xlink:label="dei_EntityIncorporationStateCountryCode" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="dei_EntityIncorporationStateCountryCode" xlink:to="dei_EntityIncorporationStateCountryCode_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="dei_EntityIncorporationStateCountryCode_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Entity Incorporation, State or Country Code</label>
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_EntityFileNumber" xlink:label="dei_EntityFileNumber" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="dei_EntityFileNumber" xlink:to="dei_EntityFileNumber_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="dei_EntityFileNumber_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Entity File Number</label>
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_EntityTaxIdentificationNumber" xlink:label="dei_EntityTaxIdentificationNumber" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="dei_EntityTaxIdentificationNumber" xlink:to="dei_EntityTaxIdentificationNumber_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="dei_EntityTaxIdentificationNumber_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Entity Tax Identification Number</label>
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_EntityAddressAddressLine1" xlink:label="dei_EntityAddressAddressLine1" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="dei_EntityAddressAddressLine1" xlink:to="dei_EntityAddressAddressLine1_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="dei_EntityAddressAddressLine1_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Entity Address, Address Line One</label>
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_EntityAddressCityOrTown" xlink:label="dei_EntityAddressCityOrTown" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="dei_EntityAddressCityOrTown" xlink:to="dei_EntityAddressCityOrTown_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="dei_EntityAddressCityOrTown_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Entity Address, City or Town</label>
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_EntityAddressStateOrProvince" xlink:label="dei_EntityAddressStateOrProvince" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="dei_EntityAddressStateOrProvince" xlink:to="dei_EntityAddressStateOrProvince_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="dei_EntityAddressStateOrProvince_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Entity Address, State or Province</label>
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_EntityAddressPostalZipCode" xlink:label="dei_EntityAddressPostalZipCode" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="dei_EntityAddressPostalZipCode" xlink:to="dei_EntityAddressPostalZipCode_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="dei_EntityAddressPostalZipCode_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Entity Address, Postal Zip Code</label>
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_CityAreaCode" xlink:label="dei_CityAreaCode" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="dei_CityAreaCode" xlink:to="dei_CityAreaCode_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="dei_CityAreaCode_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">City Area Code</label>
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_LocalPhoneNumber" xlink:label="dei_LocalPhoneNumber" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="dei_LocalPhoneNumber" xlink:to="dei_LocalPhoneNumber_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="dei_LocalPhoneNumber_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Local Phone Number</label>
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_WrittenCommunications" xlink:label="dei_WrittenCommunications" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="dei_WrittenCommunications" xlink:to="dei_WrittenCommunications_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="dei_WrittenCommunications_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Written Communications</label>
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_SolicitingMaterial" xlink:label="dei_SolicitingMaterial" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="dei_SolicitingMaterial" xlink:to="dei_SolicitingMaterial_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="dei_SolicitingMaterial_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Soliciting Material</label>
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_PreCommencementTenderOffer" xlink:label="dei_PreCommencementTenderOffer" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="dei_PreCommencementTenderOffer" xlink:to="dei_PreCommencementTenderOffer_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="dei_PreCommencementTenderOffer_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Pre-commencement Tender Offer</label>
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_PreCommencementIssuerTenderOffer" xlink:label="dei_PreCommencementIssuerTenderOffer" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="dei_PreCommencementIssuerTenderOffer" xlink:to="dei_PreCommencementIssuerTenderOffer_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="dei_PreCommencementIssuerTenderOffer_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Pre-commencement Issuer Tender Offer</label>
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_Security12bTitle" xlink:label="dei_Security12bTitle" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="dei_Security12bTitle" xlink:to="dei_Security12bTitle_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="dei_Security12bTitle_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Title of 12(b) Security</label>
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_TradingSymbol" xlink:label="dei_TradingSymbol" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="dei_TradingSymbol" xlink:to="dei_TradingSymbol_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="dei_TradingSymbol_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Trading Symbol</label>
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_SecurityExchangeName" xlink:label="dei_SecurityExchangeName" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="dei_SecurityExchangeName" xlink:to="dei_SecurityExchangeName_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="dei_SecurityExchangeName_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Security Exchange Name</label>
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_EntityEmergingGrowthCompany" xlink:label="dei_EntityEmergingGrowthCompany" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="dei_EntityEmergingGrowthCompany" xlink:to="dei_EntityEmergingGrowthCompany_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="dei_EntityEmergingGrowthCompany_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Entity Emerging Growth Company</label>
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_EntityCentralIndexKey" xlink:label="dei_EntityCentralIndexKey" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="dei_EntityCentralIndexKey" xlink:to="dei_EntityCentralIndexKey_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="dei_EntityCentralIndexKey_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Entity Central Index Key</label>
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_AmendmentFlag" xlink:label="dei_AmendmentFlag" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="dei_AmendmentFlag" xlink:to="dei_AmendmentFlag_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="dei_AmendmentFlag_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Amendment Flag</label>
    <loc xlink:type="locator" xlink:href="http://xbrl.fasb.org/us-gaap/2019/elts/us-gaap-2019-01-31.xsd#us-gaap_StatementClassOfStockAxis" xlink:label="us-gaap_StatementClassOfStockAxis" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="us-gaap_StatementClassOfStockAxis" xlink:to="us-gaap_StatementClassOfStockAxis_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="us-gaap_StatementClassOfStockAxis_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Class of Stock [Axis]</label>
    <loc xlink:type="locator" xlink:href="http://xbrl.fasb.org/us-gaap/2019/elts/us-gaap-2019-01-31.xsd#us-gaap_ClassOfStockDomain" xlink:label="us-gaap_ClassOfStockDomain" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="us-gaap_ClassOfStockDomain" xlink:to="us-gaap_ClassOfStockDomain_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="us-gaap_ClassOfStockDomain_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Class Of Stock [Domain]</label>
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/terseLabel" xlink:label="us-gaap_ClassOfStockDomain_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Class of Stock [Domain]</label>
    <loc xlink:type="locator" xlink:href="lmca-20191126.xsd#lmca_LibertySiriusXmGroupCommonClassMember" xlink:label="lmca_LibertySiriusXmGroupCommonClassMember" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="lmca_LibertySiriusXmGroupCommonClassMember" xlink:to="lmca_LibertySiriusXmGroupCommonClassMember_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/documentation" xlink:label="lmca_LibertySiriusXmGroupCommonClassMember_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Classification of Liberty Sirius XM Group common stock representing ownership interest in a corporation.</label>
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="lmca_LibertySiriusXmGroupCommonClassMember_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Liberty Sirius Xm Group Common Class [Member]</label>
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/terseLabel" xlink:label="lmca_LibertySiriusXmGroupCommonClassMember_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Liberty Sirius XM Group Common Class A</label>
    <loc xlink:type="locator" xlink:href="lmca-20191126.xsd#lmca_LibertySiriusXmGroupCommonClassBMember" xlink:label="lmca_LibertySiriusXmGroupCommonClassBMember" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="lmca_LibertySiriusXmGroupCommonClassBMember" xlink:to="lmca_LibertySiriusXmGroupCommonClassBMember_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/documentation" xlink:label="lmca_LibertySiriusXmGroupCommonClassBMember_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Classification of Liberty Sirius XM Group common stock that has different rights than Common Class A, representing ownership interest in a corporation.</label>
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="lmca_LibertySiriusXmGroupCommonClassBMember_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Liberty Sirius Xm Group Common Class B [Member]</label>
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/terseLabel" xlink:label="lmca_LibertySiriusXmGroupCommonClassBMember_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Liberty Sirius XM Group Common Class B</label>
    <loc xlink:type="locator" xlink:href="lmca-20191126.xsd#lmca_LibertySiriusXmGroupCommonClassCMember" xlink:label="lmca_LibertySiriusXmGroupCommonClassCMember" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="lmca_LibertySiriusXmGroupCommonClassCMember" xlink:to="lmca_LibertySiriusXmGroupCommonClassCMember_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/documentation" xlink:label="lmca_LibertySiriusXmGroupCommonClassCMember_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Classification of Liberty Sirius XM Group common stock that has different rights than provided to Class A or B shares, representing ownership interest in a corporation.</label>
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="lmca_LibertySiriusXmGroupCommonClassCMember_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Liberty Sirius Xm Group Common Class C [Member]</label>
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/terseLabel" xlink:label="lmca_LibertySiriusXmGroupCommonClassCMember_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Liberty Sirius Xm Group Common Class C</label>
    <loc xlink:type="locator" xlink:href="lmca-20191126.xsd#lmca_LibertyBravesGroupCommonClassMember" xlink:label="lmca_LibertyBravesGroupCommonClassMember" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="lmca_LibertyBravesGroupCommonClassMember" xlink:to="lmca_LibertyBravesGroupCommonClassMember_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/documentation" xlink:label="lmca_LibertyBravesGroupCommonClassMember_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Classification of Liberty Braves Group common stock representing ownership interest in a corporation.</label>
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="lmca_LibertyBravesGroupCommonClassMember_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Liberty Braves Group Common Class [Member]</label>
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/terseLabel" xlink:label="lmca_LibertyBravesGroupCommonClassMember_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Liberty Braves Group Common Class A</label>
    <loc xlink:type="locator" xlink:href="lmca-20191126.xsd#lmca_LibertyBravesGroupCommonClassCMember" xlink:label="lmca_LibertyBravesGroupCommonClassCMember" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="lmca_LibertyBravesGroupCommonClassCMember" xlink:to="lmca_LibertyBravesGroupCommonClassCMember_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/documentation" xlink:label="lmca_LibertyBravesGroupCommonClassCMember_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Classification of Liberty Braves Group common stock that has different rights than provided to Class A or B shares, representing ownership interest in a corporation.</label>
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="lmca_LibertyBravesGroupCommonClassCMember_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Liberty Braves Group Common Class C [Member]</label>
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/terseLabel" xlink:label="lmca_LibertyBravesGroupCommonClassCMember_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Liberty Braves Group Common Class C</label>
    <loc xlink:type="locator" xlink:href="lmca-20191126.xsd#lmca_LibertyFormulaOneGroupCommonClassMember" xlink:label="lmca_LibertyFormulaOneGroupCommonClassMember" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="lmca_LibertyFormulaOneGroupCommonClassMember" xlink:to="lmca_LibertyFormulaOneGroupCommonClassMember_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/documentation" xlink:label="lmca_LibertyFormulaOneGroupCommonClassMember_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Classification of Liberty Formula One Group common stock representing ownership interest in a corporation.</label>
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="lmca_LibertyFormulaOneGroupCommonClassMember_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Liberty Formula One Group Common Class [Member]</label>
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/terseLabel" xlink:label="lmca_LibertyFormulaOneGroupCommonClassMember_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Liberty Formula One Group Common Class A</label>
    <loc xlink:type="locator" xlink:href="lmca-20191126.xsd#lmca_LibertyFormulaOneGroupCommonClassCMember" xlink:label="lmca_LibertyFormulaOneGroupCommonClassCMember" xmlns="http://www.xbrl.org/2003/linkbase" />
    <labelArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/concept-label" xlink:from="lmca_LibertyFormulaOneGroupCommonClassCMember" xlink:to="lmca_LibertyFormulaOneGroupCommonClassCMember_lbl" xmlns="http://www.xbrl.org/2003/linkbase" />
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/documentation" xlink:label="lmca_LibertyFormulaOneGroupCommonClassCMember_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Classification of Liberty Formula One Group common stock that has different rights than provided to Class A or B shares, representing ownership interest in a corporation.</label>
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/label" xlink:label="lmca_LibertyFormulaOneGroupCommonClassCMember_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Liberty Formula One Group Common Class C [Member]</label>
    <label xlink:type="resource" xlink:role="http://www.xbrl.org/2003/role/terseLabel" xlink:label="lmca_LibertyFormulaOneGroupCommonClassCMember_lbl" xml:lang="en-US" xmlns="http://www.xbrl.org/2003/linkbase">Liberty Formula One Group Common Class C</label>
  </link:labelLink>
</link:linkbase>
</XBRL>
</TEXT>
</DOCUMENT>
<DOCUMENT>
<TYPE>EX-101.PRE
<SEQUENCE>6
<FILENAME>lmca-20191126_pre.xml
<DESCRIPTION>EX-101.PRE
<TEXT>
<XBRL>
<?xml version="1.0" encoding="us-ascii"?>
<!--XBRL document created with Toppan Merrill Bridge  9.4.7193.41938 -->
<!--Based on XBRL 2.1-->
<!--Created on: 11/26/2019 9:35:32 PM-->
<!--Modified on: 11/26/2019 9:35:32 PM-->
<link:linkbase xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.xbrl.org/2003/linkbase http://www.xbrl.org/2003/xbrl-linkbase-2003-12-31.xsd" xmlns:link="http://www.xbrl.org/2003/linkbase" xmlns:xbrli="http://www.xbrl.org/2003/instance" xmlns:xlink="http://www.w3.org/1999/xlink">
  <roleRef xlink:type="simple" xlink:href="lmca-20191126.xsd#DocumentDocumentAndEntityInformation" roleURI="http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation" xmlns="http://www.xbrl.org/2003/linkbase" />
  <presentationLink xlink:role="http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation" xlink:type="extended" xlink:title="00090 - Document - Document and Entity Information" xmlns="http://www.xbrl.org/2003/linkbase">
    <loc xlink:type="locator" xlink:href="lmca-20191126.xsd#lmca_DocumentAndEntityInformationAbstract" xlink:label="lmca_DocumentAndEntityInformationAbstract" />
    <loc xlink:type="locator" xlink:href="http://xbrl.fasb.org/us-gaap/2019/elts/us-gaap-2019-01-31.xsd#us-gaap_StatementTable" xlink:label="us-gaap_StatementTable_637104009323517790" />
    <presentationArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/parent-child" xlink:from="lmca_DocumentAndEntityInformationAbstract" xlink:to="us-gaap_StatementTable_637104009323517790" order="1" use="optional" priority="2" />
    <loc xlink:type="locator" xlink:href="http://xbrl.fasb.org/us-gaap/2019/elts/us-gaap-2019-01-31.xsd#us-gaap_StatementClassOfStockAxis" xlink:label="us-gaap_StatementClassOfStockAxis_637104009323517790" />
    <presentationArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/parent-child" xlink:from="us-gaap_StatementTable_637104009323517790" xlink:to="us-gaap_StatementClassOfStockAxis_637104009323517790" order="1" use="optional" priority="2" />
    <loc xlink:type="locator" xlink:href="http://xbrl.fasb.org/us-gaap/2019/elts/us-gaap-2019-01-31.xsd#us-gaap_ClassOfStockDomain" xlink:label="us-gaap_ClassOfStockDomain_637104009323517790" />
    <presentationArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/parent-child" xlink:from="us-gaap_StatementClassOfStockAxis_637104009323517790" xlink:to="us-gaap_ClassOfStockDomain_637104009323517790" order="1" use="optional" preferredLabel="http://www.xbrl.org/2003/role/terseLabel" />
    <loc xlink:type="locator" xlink:href="lmca-20191126.xsd#lmca_LibertySiriusXmGroupCommonClassMember" xlink:label="lmca_LibertySiriusXmGroupCommonClassMember_637104009323527807" />
    <presentationArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/parent-child" xlink:from="us-gaap_ClassOfStockDomain_637104009323517790" xlink:to="lmca_LibertySiriusXmGroupCommonClassMember_637104009323527807" order="1" use="optional" preferredLabel="http://www.xbrl.org/2003/role/terseLabel" />
    <loc xlink:type="locator" xlink:href="lmca-20191126.xsd#lmca_LibertySiriusXmGroupCommonClassBMember" xlink:label="lmca_LibertySiriusXmGroupCommonClassBMember_637104009323527807" />
    <presentationArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/parent-child" xlink:from="us-gaap_ClassOfStockDomain_637104009323517790" xlink:to="lmca_LibertySiriusXmGroupCommonClassBMember_637104009323527807" order="2" use="optional" preferredLabel="http://www.xbrl.org/2003/role/terseLabel" />
    <loc xlink:type="locator" xlink:href="lmca-20191126.xsd#lmca_LibertySiriusXmGroupCommonClassCMember" xlink:label="lmca_LibertySiriusXmGroupCommonClassCMember_637104009323527807" />
    <presentationArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/parent-child" xlink:from="us-gaap_ClassOfStockDomain_637104009323517790" xlink:to="lmca_LibertySiriusXmGroupCommonClassCMember_637104009323527807" order="3" use="optional" preferredLabel="http://www.xbrl.org/2003/role/terseLabel" />
    <loc xlink:type="locator" xlink:href="lmca-20191126.xsd#lmca_LibertyBravesGroupCommonClassMember" xlink:label="lmca_LibertyBravesGroupCommonClassMember_637104009323527807" />
    <presentationArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/parent-child" xlink:from="us-gaap_ClassOfStockDomain_637104009323517790" xlink:to="lmca_LibertyBravesGroupCommonClassMember_637104009323527807" order="4" use="optional" preferredLabel="http://www.xbrl.org/2003/role/terseLabel" />
    <loc xlink:type="locator" xlink:href="lmca-20191126.xsd#lmca_LibertyBravesGroupCommonClassCMember" xlink:label="lmca_LibertyBravesGroupCommonClassCMember_637104009323527807" />
    <presentationArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/parent-child" xlink:from="us-gaap_ClassOfStockDomain_637104009323517790" xlink:to="lmca_LibertyBravesGroupCommonClassCMember_637104009323527807" order="5" use="optional" preferredLabel="http://www.xbrl.org/2003/role/terseLabel" />
    <loc xlink:type="locator" xlink:href="lmca-20191126.xsd#lmca_LibertyFormulaOneGroupCommonClassMember" xlink:label="lmca_LibertyFormulaOneGroupCommonClassMember_637104009323527807" />
    <presentationArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/parent-child" xlink:from="us-gaap_ClassOfStockDomain_637104009323517790" xlink:to="lmca_LibertyFormulaOneGroupCommonClassMember_637104009323527807" order="6" use="optional" preferredLabel="http://www.xbrl.org/2003/role/terseLabel" />
    <loc xlink:type="locator" xlink:href="lmca-20191126.xsd#lmca_LibertyFormulaOneGroupCommonClassCMember" xlink:label="lmca_LibertyFormulaOneGroupCommonClassCMember_637104009323527807" />
    <presentationArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/parent-child" xlink:from="us-gaap_ClassOfStockDomain_637104009323517790" xlink:to="lmca_LibertyFormulaOneGroupCommonClassCMember_637104009323527807" order="7" use="optional" preferredLabel="http://www.xbrl.org/2003/role/terseLabel" />
    <loc xlink:type="locator" xlink:href="http://xbrl.fasb.org/us-gaap/2019/elts/us-gaap-2019-01-31.xsd#us-gaap_StatementLineItems" xlink:label="us-gaap_StatementLineItems_637104009323527807" />
    <presentationArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/parent-child" xlink:from="us-gaap_StatementTable_637104009323517790" xlink:to="us-gaap_StatementLineItems_637104009323527807" order="2" use="optional" priority="2" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_DocumentType" xlink:label="dei_DocumentType_637104009323527807" />
    <presentationArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/parent-child" xlink:from="us-gaap_StatementLineItems_637104009323527807" xlink:to="dei_DocumentType_637104009323527807" order="1" use="optional" priority="1" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_DocumentPeriodEndDate" xlink:label="dei_DocumentPeriodEndDate_637104009323527807" />
    <presentationArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/parent-child" xlink:from="us-gaap_StatementLineItems_637104009323527807" xlink:to="dei_DocumentPeriodEndDate_637104009323527807" order="2" use="optional" priority="1" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_EntityRegistrantName" xlink:label="dei_EntityRegistrantName_637104009323527807" />
    <presentationArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/parent-child" xlink:from="us-gaap_StatementLineItems_637104009323527807" xlink:to="dei_EntityRegistrantName_637104009323527807" order="3" use="optional" priority="1" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_EntityIncorporationStateCountryCode" xlink:label="dei_EntityIncorporationStateCountryCode_637104009323527807" />
    <presentationArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/parent-child" xlink:from="us-gaap_StatementLineItems_637104009323527807" xlink:to="dei_EntityIncorporationStateCountryCode_637104009323527807" order="4" use="optional" priority="1" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_EntityFileNumber" xlink:label="dei_EntityFileNumber_637104009323537802" />
    <presentationArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/parent-child" xlink:from="us-gaap_StatementLineItems_637104009323527807" xlink:to="dei_EntityFileNumber_637104009323537802" order="5" use="optional" priority="2" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_EntityTaxIdentificationNumber" xlink:label="dei_EntityTaxIdentificationNumber_637104009323537802" />
    <presentationArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/parent-child" xlink:from="us-gaap_StatementLineItems_637104009323527807" xlink:to="dei_EntityTaxIdentificationNumber_637104009323537802" order="6" use="optional" priority="1" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_EntityAddressAddressLine1" xlink:label="dei_EntityAddressAddressLine1_637104009323537802" />
    <presentationArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/parent-child" xlink:from="us-gaap_StatementLineItems_637104009323527807" xlink:to="dei_EntityAddressAddressLine1_637104009323537802" order="7" use="optional" priority="1" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_EntityAddressCityOrTown" xlink:label="dei_EntityAddressCityOrTown_637104009323537802" />
    <presentationArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/parent-child" xlink:from="us-gaap_StatementLineItems_637104009323527807" xlink:to="dei_EntityAddressCityOrTown_637104009323537802" order="8" use="optional" priority="1" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_EntityAddressStateOrProvince" xlink:label="dei_EntityAddressStateOrProvince_637104009323537802" />
    <presentationArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/parent-child" xlink:from="us-gaap_StatementLineItems_637104009323527807" xlink:to="dei_EntityAddressStateOrProvince_637104009323537802" order="9" use="optional" priority="2" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_EntityAddressPostalZipCode" xlink:label="dei_EntityAddressPostalZipCode_637104009323537802" />
    <presentationArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/parent-child" xlink:from="us-gaap_StatementLineItems_637104009323527807" xlink:to="dei_EntityAddressPostalZipCode_637104009323537802" order="10" use="optional" priority="1" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_CityAreaCode" xlink:label="dei_CityAreaCode_637104009323537802" />
    <presentationArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/parent-child" xlink:from="us-gaap_StatementLineItems_637104009323527807" xlink:to="dei_CityAreaCode_637104009323537802" order="11" use="optional" priority="1" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_LocalPhoneNumber" xlink:label="dei_LocalPhoneNumber_637104009323537802" />
    <presentationArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/parent-child" xlink:from="us-gaap_StatementLineItems_637104009323527807" xlink:to="dei_LocalPhoneNumber_637104009323537802" order="12" use="optional" priority="1" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_WrittenCommunications" xlink:label="dei_WrittenCommunications_637104009323537802" />
    <presentationArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/parent-child" xlink:from="us-gaap_StatementLineItems_637104009323527807" xlink:to="dei_WrittenCommunications_637104009323537802" order="13" use="optional" priority="3" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_SolicitingMaterial" xlink:label="dei_SolicitingMaterial_637104009323537802" />
    <presentationArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/parent-child" xlink:from="us-gaap_StatementLineItems_637104009323527807" xlink:to="dei_SolicitingMaterial_637104009323537802" order="14" use="optional" priority="3" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_PreCommencementTenderOffer" xlink:label="dei_PreCommencementTenderOffer_637104009323548269" />
    <presentationArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/parent-child" xlink:from="us-gaap_StatementLineItems_637104009323527807" xlink:to="dei_PreCommencementTenderOffer_637104009323548269" order="15" use="optional" priority="3" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_PreCommencementIssuerTenderOffer" xlink:label="dei_PreCommencementIssuerTenderOffer_637104009323548269" />
    <presentationArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/parent-child" xlink:from="us-gaap_StatementLineItems_637104009323527807" xlink:to="dei_PreCommencementIssuerTenderOffer_637104009323548269" order="16" use="optional" priority="3" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_Security12bTitle" xlink:label="dei_Security12bTitle_637104009323548269" />
    <presentationArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/parent-child" xlink:from="us-gaap_StatementLineItems_637104009323527807" xlink:to="dei_Security12bTitle_637104009323548269" order="17" use="optional" priority="1" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_TradingSymbol" xlink:label="dei_TradingSymbol_637104009323548269" />
    <presentationArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/parent-child" xlink:from="us-gaap_StatementLineItems_637104009323527807" xlink:to="dei_TradingSymbol_637104009323548269" order="18" use="optional" priority="1" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_SecurityExchangeName" xlink:label="dei_SecurityExchangeName_637104009323548269" />
    <presentationArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/parent-child" xlink:from="us-gaap_StatementLineItems_637104009323527807" xlink:to="dei_SecurityExchangeName_637104009323548269" order="19" use="optional" priority="2" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_EntityEmergingGrowthCompany" xlink:label="dei_EntityEmergingGrowthCompany_637104009323548269" />
    <presentationArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/parent-child" xlink:from="us-gaap_StatementLineItems_637104009323527807" xlink:to="dei_EntityEmergingGrowthCompany_637104009323548269" order="20" use="optional" priority="1" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_EntityCentralIndexKey" xlink:label="dei_EntityCentralIndexKey_637104009323548269" />
    <presentationArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/parent-child" xlink:from="us-gaap_StatementLineItems_637104009323527807" xlink:to="dei_EntityCentralIndexKey_637104009323548269" order="21" use="optional" priority="1" />
    <loc xlink:type="locator" xlink:href="https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd#dei_AmendmentFlag" xlink:label="dei_AmendmentFlag_637104009323548269" />
    <presentationArc xlink:type="arc" xlink:arcrole="http://www.xbrl.org/2003/arcrole/parent-child" xlink:from="us-gaap_StatementLineItems_637104009323527807" xlink:to="dei_AmendmentFlag_637104009323548269" order="22" use="optional" priority="1" />
  </presentationLink>
</link:linkbase>
</XBRL>
</TEXT>
</DOCUMENT>
<DOCUMENT>
<TYPE>XML
<SEQUENCE>7
<FILENAME>R1.htm
<DESCRIPTION>IDEA: XBRL DOCUMENT
<TEXT>
<html>
<head>
<title></title>
<link rel="stylesheet" type="text/css" href="report.css">
<script type="text/javascript" src="Show.js">/* Do Not Remove This Comment */</script><script type="text/javascript">
							function toggleNextSibling (e) {
							if (e.nextSibling.style.display=='none') {
							e.nextSibling.style.display='block';
							} else { e.nextSibling.style.display='none'; }
							}</script>
</head>
<body>
<span style="display: none;">v3.19.3</span><table class="report" border="0" cellspacing="2" id="idp6658619168">
<tr>
<th class="tl" colspan="1" rowspan="1"><div style="width: 200px;"><strong>Document and Entity Information<br></strong></div></th>
<th class="th"><div>Nov. 26, 2019</div></th>
</tr>
<tr class="re">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_DocumentType', window );">Document Type</a></td>
<td class="text">8-K<span></span>
</td>
</tr>
<tr class="ro">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_DocumentPeriodEndDate', window );">Document Period End Date</a></td>
<td class="text">Nov. 26,  2019<span></span>
</td>
</tr>
<tr class="re">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_EntityRegistrantName', window );">Entity Registrant Name</a></td>
<td class="text">LIBERTY MEDIA CORPORATION<span></span>
</td>
</tr>
<tr class="ro">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_EntityIncorporationStateCountryCode', window );">Entity Incorporation, State or Country Code</a></td>
<td class="text">DE<span></span>
</td>
</tr>
<tr class="re">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_EntityFileNumber', window );">Entity File Number</a></td>
<td class="text">001-35707<span></span>
</td>
</tr>
<tr class="ro">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_EntityTaxIdentificationNumber', window );">Entity Tax Identification Number</a></td>
<td class="text">37-1699499<span></span>
</td>
</tr>
<tr class="re">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_EntityAddressAddressLine1', window );">Entity Address, Address Line One</a></td>
<td class="text">12300 Liberty Blvd.<span></span>
</td>
</tr>
<tr class="ro">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_EntityAddressCityOrTown', window );">Entity Address, City or Town</a></td>
<td class="text">Englewood<span></span>
</td>
</tr>
<tr class="re">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_EntityAddressStateOrProvince', window );">Entity Address, State or Province</a></td>
<td class="text">CO<span></span>
</td>
</tr>
<tr class="ro">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_EntityAddressPostalZipCode', window );">Entity Address, Postal Zip Code</a></td>
<td class="text">80112<span></span>
</td>
</tr>
<tr class="re">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_CityAreaCode', window );">City Area Code</a></td>
<td class="text">720<span></span>
</td>
</tr>
<tr class="ro">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_LocalPhoneNumber', window );">Local Phone Number</a></td>
<td class="text">875-5400<span></span>
</td>
</tr>
<tr class="re">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_WrittenCommunications', window );">Written Communications</a></td>
<td class="text">false<span></span>
</td>
</tr>
<tr class="ro">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_SolicitingMaterial', window );">Soliciting Material</a></td>
<td class="text">false<span></span>
</td>
</tr>
<tr class="re">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_PreCommencementTenderOffer', window );">Pre-commencement Tender Offer</a></td>
<td class="text">false<span></span>
</td>
</tr>
<tr class="ro">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_PreCommencementIssuerTenderOffer', window );">Pre-commencement Issuer Tender Offer</a></td>
<td class="text">false<span></span>
</td>
</tr>
<tr class="re">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_EntityEmergingGrowthCompany', window );">Entity Emerging Growth Company</a></td>
<td class="text">false<span></span>
</td>
</tr>
<tr class="ro">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_EntityCentralIndexKey', window );">Entity Central Index Key</a></td>
<td class="text">0001560385<span></span>
</td>
</tr>
<tr class="re">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_AmendmentFlag', window );">Amendment Flag</a></td>
<td class="text">false<span></span>
</td>
</tr>
<tr class="rh">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_us-gaap_StatementClassOfStockAxis=lmca_LibertySiriusXmGroupCommonClassMember', window );">Liberty Sirius XM Group Common Class A</a></td>
<td class="text">&#160;<span></span>
</td>
</tr>
<tr class="re">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_Security12bTitle', window );">Title of 12(b) Security</a></td>
<td class="text">Series A Liberty SiriusXM Common Stock <span></span>
</td>
</tr>
<tr class="ro">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_TradingSymbol', window );">Trading Symbol</a></td>
<td class="text">LSXMA<span></span>
</td>
</tr>
<tr class="re">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_SecurityExchangeName', window );">Security Exchange Name</a></td>
<td class="text">NASDAQ<span></span>
</td>
</tr>
<tr class="rh">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_us-gaap_StatementClassOfStockAxis=lmca_LibertySiriusXmGroupCommonClassBMember', window );">Liberty Sirius XM Group Common Class B</a></td>
<td class="text">&#160;<span></span>
</td>
</tr>
<tr class="re">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_Security12bTitle', window );">Title of 12(b) Security</a></td>
<td class="text">Series B Liberty SiriusXM Common Stock<span></span>
</td>
</tr>
<tr class="ro">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_TradingSymbol', window );">Trading Symbol</a></td>
<td class="text">LSXMB<span></span>
</td>
</tr>
<tr class="re">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_SecurityExchangeName', window );">Security Exchange Name</a></td>
<td class="text">NASDAQ<span></span>
</td>
</tr>
<tr class="rh">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_us-gaap_StatementClassOfStockAxis=lmca_LibertySiriusXmGroupCommonClassCMember', window );">Liberty Sirius Xm Group Common Class C</a></td>
<td class="text">&#160;<span></span>
</td>
</tr>
<tr class="re">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_Security12bTitle', window );">Title of 12(b) Security</a></td>
<td class="text">Series C Liberty SiriusXM Common Stock<span></span>
</td>
</tr>
<tr class="ro">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_TradingSymbol', window );">Trading Symbol</a></td>
<td class="text">LSXMK<span></span>
</td>
</tr>
<tr class="re">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_SecurityExchangeName', window );">Security Exchange Name</a></td>
<td class="text">NASDAQ<span></span>
</td>
</tr>
<tr class="rh">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_us-gaap_StatementClassOfStockAxis=lmca_LibertyBravesGroupCommonClassMember', window );">Liberty Braves Group Common Class A</a></td>
<td class="text">&#160;<span></span>
</td>
</tr>
<tr class="re">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_Security12bTitle', window );">Title of 12(b) Security</a></td>
<td class="text">Series A Liberty Braves Common Stock<span></span>
</td>
</tr>
<tr class="ro">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_TradingSymbol', window );">Trading Symbol</a></td>
<td class="text">BATRA<span></span>
</td>
</tr>
<tr class="re">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_SecurityExchangeName', window );">Security Exchange Name</a></td>
<td class="text">NASDAQ<span></span>
</td>
</tr>
<tr class="rh">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_us-gaap_StatementClassOfStockAxis=lmca_LibertyBravesGroupCommonClassCMember', window );">Liberty Braves Group Common Class C</a></td>
<td class="text">&#160;<span></span>
</td>
</tr>
<tr class="re">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_Security12bTitle', window );">Title of 12(b) Security</a></td>
<td class="text">Series C Liberty Braves Common Stock<span></span>
</td>
</tr>
<tr class="ro">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_TradingSymbol', window );">Trading Symbol</a></td>
<td class="text">BATRK<span></span>
</td>
</tr>
<tr class="re">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_SecurityExchangeName', window );">Security Exchange Name</a></td>
<td class="text">NASDAQ<span></span>
</td>
</tr>
<tr class="rh">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_us-gaap_StatementClassOfStockAxis=lmca_LibertyFormulaOneGroupCommonClassMember', window );">Liberty Formula One Group Common Class A</a></td>
<td class="text">&#160;<span></span>
</td>
</tr>
<tr class="re">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_Security12bTitle', window );">Title of 12(b) Security</a></td>
<td class="text">Series A Liberty Formula One Common Stock<span></span>
</td>
</tr>
<tr class="ro">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_TradingSymbol', window );">Trading Symbol</a></td>
<td class="text">FWONA<span></span>
</td>
</tr>
<tr class="re">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_SecurityExchangeName', window );">Security Exchange Name</a></td>
<td class="text">NASDAQ<span></span>
</td>
</tr>
<tr class="rh">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_us-gaap_StatementClassOfStockAxis=lmca_LibertyFormulaOneGroupCommonClassCMember', window );">Liberty Formula One Group Common Class C</a></td>
<td class="text">&#160;<span></span>
</td>
</tr>
<tr class="re">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_Security12bTitle', window );">Title of 12(b) Security</a></td>
<td class="text">Series C Liberty Formula One Common Stock<span></span>
</td>
</tr>
<tr class="ro">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_TradingSymbol', window );">Trading Symbol</a></td>
<td class="text">FWONK<span></span>
</td>
</tr>
<tr class="re">
<td class="pl " style="border-bottom: 0px;" valign="top"><a class="a" href="javascript:void(0);" onclick="top.Show.showAR( this, 'defref_dei_SecurityExchangeName', window );">Security Exchange Name</a></td>
<td class="text">NASDAQ<span></span>
</td>
</tr>
</table>
<div style="display: none;">
<table border="0" cellpadding="0" cellspacing="0" class="authRefData" style="display: none;" id="defref_dei_AmendmentFlag">
<tr><td class="hide"><a style="color: white;" href="javascript:void(0);" onclick="top.Show.hideAR();">X</a></td></tr>
<tr><td><div class="body" style="padding: 2px;">
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">- Definition</a><div><p>Boolean flag that is true when the XBRL content amends previously-filed or accepted submission.</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ References</a><div style="display: none;"><p>No definition available.</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ Details</a><div style="display: none;"><table border="0" cellpadding="0" cellspacing="0">
<tr>
<td><strong> Name:</strong></td>
<td style="white-space:nowrap;">dei_AmendmentFlag</td>
</tr>
<tr>
<td style="padding-right: 4px;white-space:nowrap;"><strong> Namespace Prefix:</strong></td>
<td>dei_</td>
</tr>
<tr>
<td><strong> Data Type:</strong></td>
<td>xbrli:booleanItemType</td>
</tr>
<tr>
<td><strong> Balance Type:</strong></td>
<td>na</td>
</tr>
<tr>
<td><strong> Period Type:</strong></td>
<td>duration</td>
</tr>
</table></div>
</div></td></tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" class="authRefData" style="display: none;" id="defref_dei_CityAreaCode">
<tr><td class="hide"><a style="color: white;" href="javascript:void(0);" onclick="top.Show.hideAR();">X</a></td></tr>
<tr><td><div class="body" style="padding: 2px;">
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">- Definition</a><div><p>Area code of city</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ References</a><div style="display: none;"><p>No definition available.</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ Details</a><div style="display: none;"><table border="0" cellpadding="0" cellspacing="0">
<tr>
<td><strong> Name:</strong></td>
<td style="white-space:nowrap;">dei_CityAreaCode</td>
</tr>
<tr>
<td style="padding-right: 4px;white-space:nowrap;"><strong> Namespace Prefix:</strong></td>
<td>dei_</td>
</tr>
<tr>
<td><strong> Data Type:</strong></td>
<td>xbrli:normalizedStringItemType</td>
</tr>
<tr>
<td><strong> Balance Type:</strong></td>
<td>na</td>
</tr>
<tr>
<td><strong> Period Type:</strong></td>
<td>duration</td>
</tr>
</table></div>
</div></td></tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" class="authRefData" style="display: none;" id="defref_dei_DocumentPeriodEndDate">
<tr><td class="hide"><a style="color: white;" href="javascript:void(0);" onclick="top.Show.hideAR();">X</a></td></tr>
<tr><td><div class="body" style="padding: 2px;">
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">- Definition</a><div><p>The end date of the period reflected on the cover page if a periodic report. For all other reports and registration statements containing historical data, it is the date up through which that historical data is presented.  If there is no historical data in the report, use the filing date. The format of the date is CCYY-MM-DD.</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ References</a><div style="display: none;"><p>No definition available.</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ Details</a><div style="display: none;"><table border="0" cellpadding="0" cellspacing="0">
<tr>
<td><strong> Name:</strong></td>
<td style="white-space:nowrap;">dei_DocumentPeriodEndDate</td>
</tr>
<tr>
<td style="padding-right: 4px;white-space:nowrap;"><strong> Namespace Prefix:</strong></td>
<td>dei_</td>
</tr>
<tr>
<td><strong> Data Type:</strong></td>
<td>xbrli:dateItemType</td>
</tr>
<tr>
<td><strong> Balance Type:</strong></td>
<td>na</td>
</tr>
<tr>
<td><strong> Period Type:</strong></td>
<td>duration</td>
</tr>
</table></div>
</div></td></tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" class="authRefData" style="display: none;" id="defref_dei_DocumentType">
<tr><td class="hide"><a style="color: white;" href="javascript:void(0);" onclick="top.Show.hideAR();">X</a></td></tr>
<tr><td><div class="body" style="padding: 2px;">
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">- Definition</a><div><p>The type of document being provided (such as 10-K, 10-Q, 485BPOS, etc). The document type is limited to the same value as the supporting SEC submission type, or the word 'Other'.</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ References</a><div style="display: none;"><p>No definition available.</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ Details</a><div style="display: none;"><table border="0" cellpadding="0" cellspacing="0">
<tr>
<td><strong> Name:</strong></td>
<td style="white-space:nowrap;">dei_DocumentType</td>
</tr>
<tr>
<td style="padding-right: 4px;white-space:nowrap;"><strong> Namespace Prefix:</strong></td>
<td>dei_</td>
</tr>
<tr>
<td><strong> Data Type:</strong></td>
<td>dei:submissionTypeItemType</td>
</tr>
<tr>
<td><strong> Balance Type:</strong></td>
<td>na</td>
</tr>
<tr>
<td><strong> Period Type:</strong></td>
<td>duration</td>
</tr>
</table></div>
</div></td></tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" class="authRefData" style="display: none;" id="defref_dei_EntityAddressAddressLine1">
<tr><td class="hide"><a style="color: white;" href="javascript:void(0);" onclick="top.Show.hideAR();">X</a></td></tr>
<tr><td><div class="body" style="padding: 2px;">
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">- Definition</a><div><p>Address Line 1 such as Attn, Building Name, Street Name</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ References</a><div style="display: none;"><p>No definition available.</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ Details</a><div style="display: none;"><table border="0" cellpadding="0" cellspacing="0">
<tr>
<td><strong> Name:</strong></td>
<td style="white-space:nowrap;">dei_EntityAddressAddressLine1</td>
</tr>
<tr>
<td style="padding-right: 4px;white-space:nowrap;"><strong> Namespace Prefix:</strong></td>
<td>dei_</td>
</tr>
<tr>
<td><strong> Data Type:</strong></td>
<td>xbrli:normalizedStringItemType</td>
</tr>
<tr>
<td><strong> Balance Type:</strong></td>
<td>na</td>
</tr>
<tr>
<td><strong> Period Type:</strong></td>
<td>duration</td>
</tr>
</table></div>
</div></td></tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" class="authRefData" style="display: none;" id="defref_dei_EntityAddressCityOrTown">
<tr><td class="hide"><a style="color: white;" href="javascript:void(0);" onclick="top.Show.hideAR();">X</a></td></tr>
<tr><td><div class="body" style="padding: 2px;">
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">- Definition</a><div><p>Name of the City or Town</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ References</a><div style="display: none;"><p>No definition available.</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ Details</a><div style="display: none;"><table border="0" cellpadding="0" cellspacing="0">
<tr>
<td><strong> Name:</strong></td>
<td style="white-space:nowrap;">dei_EntityAddressCityOrTown</td>
</tr>
<tr>
<td style="padding-right: 4px;white-space:nowrap;"><strong> Namespace Prefix:</strong></td>
<td>dei_</td>
</tr>
<tr>
<td><strong> Data Type:</strong></td>
<td>xbrli:normalizedStringItemType</td>
</tr>
<tr>
<td><strong> Balance Type:</strong></td>
<td>na</td>
</tr>
<tr>
<td><strong> Period Type:</strong></td>
<td>duration</td>
</tr>
</table></div>
</div></td></tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" class="authRefData" style="display: none;" id="defref_dei_EntityAddressPostalZipCode">
<tr><td class="hide"><a style="color: white;" href="javascript:void(0);" onclick="top.Show.hideAR();">X</a></td></tr>
<tr><td><div class="body" style="padding: 2px;">
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">- Definition</a><div><p>Code for the postal or zip code</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ References</a><div style="display: none;"><p>No definition available.</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ Details</a><div style="display: none;"><table border="0" cellpadding="0" cellspacing="0">
<tr>
<td><strong> Name:</strong></td>
<td style="white-space:nowrap;">dei_EntityAddressPostalZipCode</td>
</tr>
<tr>
<td style="padding-right: 4px;white-space:nowrap;"><strong> Namespace Prefix:</strong></td>
<td>dei_</td>
</tr>
<tr>
<td><strong> Data Type:</strong></td>
<td>xbrli:normalizedStringItemType</td>
</tr>
<tr>
<td><strong> Balance Type:</strong></td>
<td>na</td>
</tr>
<tr>
<td><strong> Period Type:</strong></td>
<td>duration</td>
</tr>
</table></div>
</div></td></tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" class="authRefData" style="display: none;" id="defref_dei_EntityAddressStateOrProvince">
<tr><td class="hide"><a style="color: white;" href="javascript:void(0);" onclick="top.Show.hideAR();">X</a></td></tr>
<tr><td><div class="body" style="padding: 2px;">
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">- Definition</a><div><p>Name of the state or province.</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ References</a><div style="display: none;"><p>No definition available.</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ Details</a><div style="display: none;"><table border="0" cellpadding="0" cellspacing="0">
<tr>
<td><strong> Name:</strong></td>
<td style="white-space:nowrap;">dei_EntityAddressStateOrProvince</td>
</tr>
<tr>
<td style="padding-right: 4px;white-space:nowrap;"><strong> Namespace Prefix:</strong></td>
<td>dei_</td>
</tr>
<tr>
<td><strong> Data Type:</strong></td>
<td>dei:stateOrProvinceItemType</td>
</tr>
<tr>
<td><strong> Balance Type:</strong></td>
<td>na</td>
</tr>
<tr>
<td><strong> Period Type:</strong></td>
<td>duration</td>
</tr>
</table></div>
</div></td></tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" class="authRefData" style="display: none;" id="defref_dei_EntityCentralIndexKey">
<tr><td class="hide"><a style="color: white;" href="javascript:void(0);" onclick="top.Show.hideAR();">X</a></td></tr>
<tr><td><div class="body" style="padding: 2px;">
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">- Definition</a><div><p>A unique 10-digit SEC-issued value to identify entities that have filed disclosures with the SEC. It is commonly abbreviated as CIK.</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ References</a><div style="display: none;"><p>Reference 1: http://www.xbrl.org/2003/role/presentationRef<br> -Publisher SEC<br> -Name Regulation 12B<br> -Number 240<br> -Section 12<br> -Subsection b-2<br></p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ Details</a><div style="display: none;"><table border="0" cellpadding="0" cellspacing="0">
<tr>
<td><strong> Name:</strong></td>
<td style="white-space:nowrap;">dei_EntityCentralIndexKey</td>
</tr>
<tr>
<td style="padding-right: 4px;white-space:nowrap;"><strong> Namespace Prefix:</strong></td>
<td>dei_</td>
</tr>
<tr>
<td><strong> Data Type:</strong></td>
<td>dei:centralIndexKeyItemType</td>
</tr>
<tr>
<td><strong> Balance Type:</strong></td>
<td>na</td>
</tr>
<tr>
<td><strong> Period Type:</strong></td>
<td>duration</td>
</tr>
</table></div>
</div></td></tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" class="authRefData" style="display: none;" id="defref_dei_EntityEmergingGrowthCompany">
<tr><td class="hide"><a style="color: white;" href="javascript:void(0);" onclick="top.Show.hideAR();">X</a></td></tr>
<tr><td><div class="body" style="padding: 2px;">
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">- Definition</a><div><p>Indicate if registrant meets the emerging growth company criteria.</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ References</a><div style="display: none;"><p>Reference 1: http://www.xbrl.org/2003/role/presentationRef<br> -Publisher SEC<br> -Name Regulation 12B<br> -Number 240<br> -Section 12<br> -Subsection b-2<br></p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ Details</a><div style="display: none;"><table border="0" cellpadding="0" cellspacing="0">
<tr>
<td><strong> Name:</strong></td>
<td style="white-space:nowrap;">dei_EntityEmergingGrowthCompany</td>
</tr>
<tr>
<td style="padding-right: 4px;white-space:nowrap;"><strong> Namespace Prefix:</strong></td>
<td>dei_</td>
</tr>
<tr>
<td><strong> Data Type:</strong></td>
<td>xbrli:booleanItemType</td>
</tr>
<tr>
<td><strong> Balance Type:</strong></td>
<td>na</td>
</tr>
<tr>
<td><strong> Period Type:</strong></td>
<td>duration</td>
</tr>
</table></div>
</div></td></tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" class="authRefData" style="display: none;" id="defref_dei_EntityFileNumber">
<tr><td class="hide"><a style="color: white;" href="javascript:void(0);" onclick="top.Show.hideAR();">X</a></td></tr>
<tr><td><div class="body" style="padding: 2px;">
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">- Definition</a><div><p>Commission file number. The field allows up to 17 characters. The prefix may contain 1-3 digits, the sequence number may contain 1-8 digits, the optional suffix may contain 1-4 characters, and the fields are separated with a hyphen.</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ References</a><div style="display: none;"><p>No definition available.</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ Details</a><div style="display: none;"><table border="0" cellpadding="0" cellspacing="0">
<tr>
<td><strong> Name:</strong></td>
<td style="white-space:nowrap;">dei_EntityFileNumber</td>
</tr>
<tr>
<td style="padding-right: 4px;white-space:nowrap;"><strong> Namespace Prefix:</strong></td>
<td>dei_</td>
</tr>
<tr>
<td><strong> Data Type:</strong></td>
<td>dei:fileNumberItemType</td>
</tr>
<tr>
<td><strong> Balance Type:</strong></td>
<td>na</td>
</tr>
<tr>
<td><strong> Period Type:</strong></td>
<td>duration</td>
</tr>
</table></div>
</div></td></tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" class="authRefData" style="display: none;" id="defref_dei_EntityIncorporationStateCountryCode">
<tr><td class="hide"><a style="color: white;" href="javascript:void(0);" onclick="top.Show.hideAR();">X</a></td></tr>
<tr><td><div class="body" style="padding: 2px;">
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">- Definition</a><div><p>Two-character EDGAR code representing the state or country of incorporation.</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ References</a><div style="display: none;"><p>No definition available.</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ Details</a><div style="display: none;"><table border="0" cellpadding="0" cellspacing="0">
<tr>
<td><strong> Name:</strong></td>
<td style="white-space:nowrap;">dei_EntityIncorporationStateCountryCode</td>
</tr>
<tr>
<td style="padding-right: 4px;white-space:nowrap;"><strong> Namespace Prefix:</strong></td>
<td>dei_</td>
</tr>
<tr>
<td><strong> Data Type:</strong></td>
<td>dei:edgarStateCountryItemType</td>
</tr>
<tr>
<td><strong> Balance Type:</strong></td>
<td>na</td>
</tr>
<tr>
<td><strong> Period Type:</strong></td>
<td>duration</td>
</tr>
</table></div>
</div></td></tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" class="authRefData" style="display: none;" id="defref_dei_EntityRegistrantName">
<tr><td class="hide"><a style="color: white;" href="javascript:void(0);" onclick="top.Show.hideAR();">X</a></td></tr>
<tr><td><div class="body" style="padding: 2px;">
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">- Definition</a><div><p>The exact name of the entity filing the report as specified in its charter, which is required by forms filed with the SEC.</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ References</a><div style="display: none;"><p>Reference 1: http://www.xbrl.org/2003/role/presentationRef<br> -Publisher SEC<br> -Name Regulation 12B<br> -Number 240<br> -Section 12<br> -Subsection b-2<br></p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ Details</a><div style="display: none;"><table border="0" cellpadding="0" cellspacing="0">
<tr>
<td><strong> Name:</strong></td>
<td style="white-space:nowrap;">dei_EntityRegistrantName</td>
</tr>
<tr>
<td style="padding-right: 4px;white-space:nowrap;"><strong> Namespace Prefix:</strong></td>
<td>dei_</td>
</tr>
<tr>
<td><strong> Data Type:</strong></td>
<td>xbrli:normalizedStringItemType</td>
</tr>
<tr>
<td><strong> Balance Type:</strong></td>
<td>na</td>
</tr>
<tr>
<td><strong> Period Type:</strong></td>
<td>duration</td>
</tr>
</table></div>
</div></td></tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" class="authRefData" style="display: none;" id="defref_dei_EntityTaxIdentificationNumber">
<tr><td class="hide"><a style="color: white;" href="javascript:void(0);" onclick="top.Show.hideAR();">X</a></td></tr>
<tr><td><div class="body" style="padding: 2px;">
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">- Definition</a><div><p>The Tax Identification Number (TIN), also known as an Employer Identification Number (EIN), is a unique 9-digit value assigned by the IRS.</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ References</a><div style="display: none;"><p>Reference 1: http://www.xbrl.org/2003/role/presentationRef<br> -Publisher SEC<br> -Name Regulation 12B<br> -Number 240<br> -Section 12<br> -Subsection b-2<br></p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ Details</a><div style="display: none;"><table border="0" cellpadding="0" cellspacing="0">
<tr>
<td><strong> Name:</strong></td>
<td style="white-space:nowrap;">dei_EntityTaxIdentificationNumber</td>
</tr>
<tr>
<td style="padding-right: 4px;white-space:nowrap;"><strong> Namespace Prefix:</strong></td>
<td>dei_</td>
</tr>
<tr>
<td><strong> Data Type:</strong></td>
<td>dei:employerIdItemType</td>
</tr>
<tr>
<td><strong> Balance Type:</strong></td>
<td>na</td>
</tr>
<tr>
<td><strong> Period Type:</strong></td>
<td>duration</td>
</tr>
</table></div>
</div></td></tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" class="authRefData" style="display: none;" id="defref_dei_LocalPhoneNumber">
<tr><td class="hide"><a style="color: white;" href="javascript:void(0);" onclick="top.Show.hideAR();">X</a></td></tr>
<tr><td><div class="body" style="padding: 2px;">
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">- Definition</a><div><p>Local phone number for entity.</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ References</a><div style="display: none;"><p>No definition available.</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ Details</a><div style="display: none;"><table border="0" cellpadding="0" cellspacing="0">
<tr>
<td><strong> Name:</strong></td>
<td style="white-space:nowrap;">dei_LocalPhoneNumber</td>
</tr>
<tr>
<td style="padding-right: 4px;white-space:nowrap;"><strong> Namespace Prefix:</strong></td>
<td>dei_</td>
</tr>
<tr>
<td><strong> Data Type:</strong></td>
<td>xbrli:normalizedStringItemType</td>
</tr>
<tr>
<td><strong> Balance Type:</strong></td>
<td>na</td>
</tr>
<tr>
<td><strong> Period Type:</strong></td>
<td>duration</td>
</tr>
</table></div>
</div></td></tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" class="authRefData" style="display: none;" id="defref_dei_PreCommencementIssuerTenderOffer">
<tr><td class="hide"><a style="color: white;" href="javascript:void(0);" onclick="top.Show.hideAR();">X</a></td></tr>
<tr><td><div class="body" style="padding: 2px;">
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">- Definition</a><div><p>Boolean flag that is true when the Form 8-K filing is intended to satisfy the filing obligation of the registrant as pre-commencement communications pursuant to Rule 13e-4(c) under the Exchange Act.</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ References</a><div style="display: none;"><p>Reference 1: http://www.xbrl.org/2003/role/presentationRef<br> -Publisher SEC<br> -Name Exchange Act<br> -Number 240<br> -Section 13e<br> -Subsection 4c<br></p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ Details</a><div style="display: none;"><table border="0" cellpadding="0" cellspacing="0">
<tr>
<td><strong> Name:</strong></td>
<td style="white-space:nowrap;">dei_PreCommencementIssuerTenderOffer</td>
</tr>
<tr>
<td style="padding-right: 4px;white-space:nowrap;"><strong> Namespace Prefix:</strong></td>
<td>dei_</td>
</tr>
<tr>
<td><strong> Data Type:</strong></td>
<td>xbrli:booleanItemType</td>
</tr>
<tr>
<td><strong> Balance Type:</strong></td>
<td>na</td>
</tr>
<tr>
<td><strong> Period Type:</strong></td>
<td>duration</td>
</tr>
</table></div>
</div></td></tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" class="authRefData" style="display: none;" id="defref_dei_PreCommencementTenderOffer">
<tr><td class="hide"><a style="color: white;" href="javascript:void(0);" onclick="top.Show.hideAR();">X</a></td></tr>
<tr><td><div class="body" style="padding: 2px;">
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">- Definition</a><div><p>Boolean flag that is true when the Form 8-K filing is intended to satisfy the filing obligation of the registrant as pre-commencement communications pursuant to Rule 14d-2(b) under the Exchange Act.</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ References</a><div style="display: none;"><p>Reference 1: http://www.xbrl.org/2003/role/presentationRef<br> -Publisher SEC<br> -Name Exchange Act<br> -Number 240<br> -Section 14d<br> -Subsection 2b<br></p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ Details</a><div style="display: none;"><table border="0" cellpadding="0" cellspacing="0">
<tr>
<td><strong> Name:</strong></td>
<td style="white-space:nowrap;">dei_PreCommencementTenderOffer</td>
</tr>
<tr>
<td style="padding-right: 4px;white-space:nowrap;"><strong> Namespace Prefix:</strong></td>
<td>dei_</td>
</tr>
<tr>
<td><strong> Data Type:</strong></td>
<td>xbrli:booleanItemType</td>
</tr>
<tr>
<td><strong> Balance Type:</strong></td>
<td>na</td>
</tr>
<tr>
<td><strong> Period Type:</strong></td>
<td>duration</td>
</tr>
</table></div>
</div></td></tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" class="authRefData" style="display: none;" id="defref_dei_Security12bTitle">
<tr><td class="hide"><a style="color: white;" href="javascript:void(0);" onclick="top.Show.hideAR();">X</a></td></tr>
<tr><td><div class="body" style="padding: 2px;">
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">- Definition</a><div><p>Title of a 12(b) registered security.</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ References</a><div style="display: none;"><p>Reference 1: http://www.xbrl.org/2003/role/presentationRef<br> -Publisher SEC<br> -Name Exchange Act<br> -Number 240<br> -Section 12<br> -Subsection b<br></p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ Details</a><div style="display: none;"><table border="0" cellpadding="0" cellspacing="0">
<tr>
<td><strong> Name:</strong></td>
<td style="white-space:nowrap;">dei_Security12bTitle</td>
</tr>
<tr>
<td style="padding-right: 4px;white-space:nowrap;"><strong> Namespace Prefix:</strong></td>
<td>dei_</td>
</tr>
<tr>
<td><strong> Data Type:</strong></td>
<td>dei:securityTitleItemType</td>
</tr>
<tr>
<td><strong> Balance Type:</strong></td>
<td>na</td>
</tr>
<tr>
<td><strong> Period Type:</strong></td>
<td>duration</td>
</tr>
</table></div>
</div></td></tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" class="authRefData" style="display: none;" id="defref_dei_SecurityExchangeName">
<tr><td class="hide"><a style="color: white;" href="javascript:void(0);" onclick="top.Show.hideAR();">X</a></td></tr>
<tr><td><div class="body" style="padding: 2px;">
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">- Definition</a><div><p>Name of the Exchange on which a security is registered.</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ References</a><div style="display: none;"><p>Reference 1: http://www.xbrl.org/2003/role/presentationRef<br> -Publisher SEC<br> -Name Exchange Act<br> -Number 240<br> -Section 12<br> -Subsection d1-1<br></p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ Details</a><div style="display: none;"><table border="0" cellpadding="0" cellspacing="0">
<tr>
<td><strong> Name:</strong></td>
<td style="white-space:nowrap;">dei_SecurityExchangeName</td>
</tr>
<tr>
<td style="padding-right: 4px;white-space:nowrap;"><strong> Namespace Prefix:</strong></td>
<td>dei_</td>
</tr>
<tr>
<td><strong> Data Type:</strong></td>
<td>dei:edgarExchangeCodeItemType</td>
</tr>
<tr>
<td><strong> Balance Type:</strong></td>
<td>na</td>
</tr>
<tr>
<td><strong> Period Type:</strong></td>
<td>duration</td>
</tr>
</table></div>
</div></td></tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" class="authRefData" style="display: none;" id="defref_dei_SolicitingMaterial">
<tr><td class="hide"><a style="color: white;" href="javascript:void(0);" onclick="top.Show.hideAR();">X</a></td></tr>
<tr><td><div class="body" style="padding: 2px;">
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">- Definition</a><div><p>Boolean flag that is true when the Form 8-K filing is intended to satisfy the filing obligation of the registrant as soliciting material pursuant to Rule 14a-12 under the Exchange Act.</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ References</a><div style="display: none;"><p>Reference 1: http://www.xbrl.org/2003/role/presentationRef<br> -Publisher SEC<br> -Name Exchange Act<br> -Section 14a<br> -Number 240<br> -Subsection 12<br></p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ Details</a><div style="display: none;"><table border="0" cellpadding="0" cellspacing="0">
<tr>
<td><strong> Name:</strong></td>
<td style="white-space:nowrap;">dei_SolicitingMaterial</td>
</tr>
<tr>
<td style="padding-right: 4px;white-space:nowrap;"><strong> Namespace Prefix:</strong></td>
<td>dei_</td>
</tr>
<tr>
<td><strong> Data Type:</strong></td>
<td>xbrli:booleanItemType</td>
</tr>
<tr>
<td><strong> Balance Type:</strong></td>
<td>na</td>
</tr>
<tr>
<td><strong> Period Type:</strong></td>
<td>duration</td>
</tr>
</table></div>
</div></td></tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" class="authRefData" style="display: none;" id="defref_dei_TradingSymbol">
<tr><td class="hide"><a style="color: white;" href="javascript:void(0);" onclick="top.Show.hideAR();">X</a></td></tr>
<tr><td><div class="body" style="padding: 2px;">
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">- Definition</a><div><p>Trading symbol of an instrument as listed on an exchange.</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ References</a><div style="display: none;"><p>No definition available.</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ Details</a><div style="display: none;"><table border="0" cellpadding="0" cellspacing="0">
<tr>
<td><strong> Name:</strong></td>
<td style="white-space:nowrap;">dei_TradingSymbol</td>
</tr>
<tr>
<td style="padding-right: 4px;white-space:nowrap;"><strong> Namespace Prefix:</strong></td>
<td>dei_</td>
</tr>
<tr>
<td><strong> Data Type:</strong></td>
<td>dei:tradingSymbolItemType</td>
</tr>
<tr>
<td><strong> Balance Type:</strong></td>
<td>na</td>
</tr>
<tr>
<td><strong> Period Type:</strong></td>
<td>duration</td>
</tr>
</table></div>
</div></td></tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" class="authRefData" style="display: none;" id="defref_dei_WrittenCommunications">
<tr><td class="hide"><a style="color: white;" href="javascript:void(0);" onclick="top.Show.hideAR();">X</a></td></tr>
<tr><td><div class="body" style="padding: 2px;">
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">- Definition</a><div><p>Boolean flag that is true when the Form 8-K filing is intended to satisfy the filing obligation of the registrant as written communications pursuant to Rule 425 under the Securities Act.</p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ References</a><div style="display: none;"><p>Reference 1: http://www.xbrl.org/2003/role/presentationRef<br> -Publisher SEC<br> -Name Securities Act<br> -Number 230<br> -Section 425<br></p></div>
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">+ Details</a><div style="display: none;"><table border="0" cellpadding="0" cellspacing="0">
<tr>
<td><strong> Name:</strong></td>
<td style="white-space:nowrap;">dei_WrittenCommunications</td>
</tr>
<tr>
<td style="padding-right: 4px;white-space:nowrap;"><strong> Namespace Prefix:</strong></td>
<td>dei_</td>
</tr>
<tr>
<td><strong> Data Type:</strong></td>
<td>xbrli:booleanItemType</td>
</tr>
<tr>
<td><strong> Balance Type:</strong></td>
<td>na</td>
</tr>
<tr>
<td><strong> Period Type:</strong></td>
<td>duration</td>
</tr>
</table></div>
</div></td></tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" class="authRefData" style="display: none;" id="defref_us-gaap_StatementClassOfStockAxis=lmca_LibertySiriusXmGroupCommonClassMember">
<tr><td class="hide"><a style="color: white;" href="javascript:void(0);" onclick="top.Show.hideAR();">X</a></td></tr>
<tr><td><div class="body" style="padding: 2px;">
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">- Details</a><div><table border="0" cellpadding="0" cellspacing="0">
<tr>
<td><strong> Name:</strong></td>
<td style="white-space:nowrap;">us-gaap_StatementClassOfStockAxis=lmca_LibertySiriusXmGroupCommonClassMember</td>
</tr>
<tr>
<td style="padding-right: 4px;white-space:nowrap;"><strong> Namespace Prefix:</strong></td>
<td></td>
</tr>
<tr>
<td><strong> Data Type:</strong></td>
<td>na</td>
</tr>
<tr>
<td><strong> Balance Type:</strong></td>
<td></td>
</tr>
<tr>
<td><strong> Period Type:</strong></td>
<td></td>
</tr>
</table></div>
</div></td></tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" class="authRefData" style="display: none;" id="defref_us-gaap_StatementClassOfStockAxis=lmca_LibertySiriusXmGroupCommonClassBMember">
<tr><td class="hide"><a style="color: white;" href="javascript:void(0);" onclick="top.Show.hideAR();">X</a></td></tr>
<tr><td><div class="body" style="padding: 2px;">
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">- Details</a><div><table border="0" cellpadding="0" cellspacing="0">
<tr>
<td><strong> Name:</strong></td>
<td style="white-space:nowrap;">us-gaap_StatementClassOfStockAxis=lmca_LibertySiriusXmGroupCommonClassBMember</td>
</tr>
<tr>
<td style="padding-right: 4px;white-space:nowrap;"><strong> Namespace Prefix:</strong></td>
<td></td>
</tr>
<tr>
<td><strong> Data Type:</strong></td>
<td>na</td>
</tr>
<tr>
<td><strong> Balance Type:</strong></td>
<td></td>
</tr>
<tr>
<td><strong> Period Type:</strong></td>
<td></td>
</tr>
</table></div>
</div></td></tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" class="authRefData" style="display: none;" id="defref_us-gaap_StatementClassOfStockAxis=lmca_LibertySiriusXmGroupCommonClassCMember">
<tr><td class="hide"><a style="color: white;" href="javascript:void(0);" onclick="top.Show.hideAR();">X</a></td></tr>
<tr><td><div class="body" style="padding: 2px;">
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">- Details</a><div><table border="0" cellpadding="0" cellspacing="0">
<tr>
<td><strong> Name:</strong></td>
<td style="white-space:nowrap;">us-gaap_StatementClassOfStockAxis=lmca_LibertySiriusXmGroupCommonClassCMember</td>
</tr>
<tr>
<td style="padding-right: 4px;white-space:nowrap;"><strong> Namespace Prefix:</strong></td>
<td></td>
</tr>
<tr>
<td><strong> Data Type:</strong></td>
<td>na</td>
</tr>
<tr>
<td><strong> Balance Type:</strong></td>
<td></td>
</tr>
<tr>
<td><strong> Period Type:</strong></td>
<td></td>
</tr>
</table></div>
</div></td></tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" class="authRefData" style="display: none;" id="defref_us-gaap_StatementClassOfStockAxis=lmca_LibertyBravesGroupCommonClassMember">
<tr><td class="hide"><a style="color: white;" href="javascript:void(0);" onclick="top.Show.hideAR();">X</a></td></tr>
<tr><td><div class="body" style="padding: 2px;">
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">- Details</a><div><table border="0" cellpadding="0" cellspacing="0">
<tr>
<td><strong> Name:</strong></td>
<td style="white-space:nowrap;">us-gaap_StatementClassOfStockAxis=lmca_LibertyBravesGroupCommonClassMember</td>
</tr>
<tr>
<td style="padding-right: 4px;white-space:nowrap;"><strong> Namespace Prefix:</strong></td>
<td></td>
</tr>
<tr>
<td><strong> Data Type:</strong></td>
<td>na</td>
</tr>
<tr>
<td><strong> Balance Type:</strong></td>
<td></td>
</tr>
<tr>
<td><strong> Period Type:</strong></td>
<td></td>
</tr>
</table></div>
</div></td></tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" class="authRefData" style="display: none;" id="defref_us-gaap_StatementClassOfStockAxis=lmca_LibertyBravesGroupCommonClassCMember">
<tr><td class="hide"><a style="color: white;" href="javascript:void(0);" onclick="top.Show.hideAR();">X</a></td></tr>
<tr><td><div class="body" style="padding: 2px;">
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">- Details</a><div><table border="0" cellpadding="0" cellspacing="0">
<tr>
<td><strong> Name:</strong></td>
<td style="white-space:nowrap;">us-gaap_StatementClassOfStockAxis=lmca_LibertyBravesGroupCommonClassCMember</td>
</tr>
<tr>
<td style="padding-right: 4px;white-space:nowrap;"><strong> Namespace Prefix:</strong></td>
<td></td>
</tr>
<tr>
<td><strong> Data Type:</strong></td>
<td>na</td>
</tr>
<tr>
<td><strong> Balance Type:</strong></td>
<td></td>
</tr>
<tr>
<td><strong> Period Type:</strong></td>
<td></td>
</tr>
</table></div>
</div></td></tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" class="authRefData" style="display: none;" id="defref_us-gaap_StatementClassOfStockAxis=lmca_LibertyFormulaOneGroupCommonClassMember">
<tr><td class="hide"><a style="color: white;" href="javascript:void(0);" onclick="top.Show.hideAR();">X</a></td></tr>
<tr><td><div class="body" style="padding: 2px;">
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">- Details</a><div><table border="0" cellpadding="0" cellspacing="0">
<tr>
<td><strong> Name:</strong></td>
<td style="white-space:nowrap;">us-gaap_StatementClassOfStockAxis=lmca_LibertyFormulaOneGroupCommonClassMember</td>
</tr>
<tr>
<td style="padding-right: 4px;white-space:nowrap;"><strong> Namespace Prefix:</strong></td>
<td></td>
</tr>
<tr>
<td><strong> Data Type:</strong></td>
<td>na</td>
</tr>
<tr>
<td><strong> Balance Type:</strong></td>
<td></td>
</tr>
<tr>
<td><strong> Period Type:</strong></td>
<td></td>
</tr>
</table></div>
</div></td></tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" class="authRefData" style="display: none;" id="defref_us-gaap_StatementClassOfStockAxis=lmca_LibertyFormulaOneGroupCommonClassCMember">
<tr><td class="hide"><a style="color: white;" href="javascript:void(0);" onclick="top.Show.hideAR();">X</a></td></tr>
<tr><td><div class="body" style="padding: 2px;">
<a href="javascript:void(0);" onclick="top.Show.toggleNext( this );">- Details</a><div><table border="0" cellpadding="0" cellspacing="0">
<tr>
<td><strong> Name:</strong></td>
<td style="white-space:nowrap;">us-gaap_StatementClassOfStockAxis=lmca_LibertyFormulaOneGroupCommonClassCMember</td>
</tr>
<tr>
<td style="padding-right: 4px;white-space:nowrap;"><strong> Namespace Prefix:</strong></td>
<td></td>
</tr>
<tr>
<td><strong> Data Type:</strong></td>
<td>na</td>
</tr>
<tr>
<td><strong> Balance Type:</strong></td>
<td></td>
</tr>
<tr>
<td><strong> Period Type:</strong></td>
<td></td>
</tr>
</table></div>
</div></td></tr>
</table>
</div>
</body>
</html>
</TEXT>
</DOCUMENT>
<DOCUMENT>
<TYPE>EXCEL
<SEQUENCE>8
<FILENAME>Financial_Report.xlsx
<DESCRIPTION>IDEA: XBRL DOCUMENT
<TEXT>
begin 644 Financial_Report.xlsx
M4$L#!!0    ( ,V$>D\?(\\#P    !,"   +    7W)E;',O+G)E;'.MDD^+
MPD ,Q;]*F?L:5\'#8CUYZ6U9_ )Q)OU#.Y,A$[%^>X>];+=44/ 87O+>CT?V
M/S2@=AQ2V\54C'X(J32M:OP"2+8ECVG%D4)6:A:/FD=I(*+ML2'8K-<[D*F'
M.>RGGD7E2B.5^S3%":4A+<TXP)6E/S/WJVR;A5ND9T*YKCM+1[873T$7LF<;
M!I99-G\LCNVW<+ZT+/0:S>,*P).B0\5?UX^8 TBTH_0(:+L A#&^NQT:E8(C
M-R."?S]PN -02P,$%     @ S81Z3R?HAPZ"    L0   !    !D;V-0<F]P
M<R]A<' N>&UL38Y-"\(P$$3_2NG=;BGH06) L$?!D_>0;FP@R8;-"OGYIH(?
MMWF\81AU8\K(XK%T-8943OTJDH\ Q:X831F:3LTXXFBD(3^ G/,6+V2?$9/
M-(X'P"J8%EQV^3O8:W7..7AKQ%/25V^9"CGIYFHQ*/B76_..7+8\#?NW_+""
MWTG] E!+ P04    " #-A'I/ 2_NG.X    K @  $0   &1O8U!R;W!S+V-O
M<F4N>&ULS9+/2L0P$(=?17)OIVFA:.CFLN))07!!\1:2V=U@\X=DI-VWMZV[
M740?P&-F?OGF&YA.1Z%#PN<4(B:RF&]&U_LL=-RP(U$4 %D?T:E<3@D_-?<A
M.473,QT@*OVA#@AU5;7@D)11I& &%G$E,MD9+71"12&=\4:O^/B9^@5F-&"/
M#CUEX"4')N>)\33V'5P!,XPPN?Q=0+,2E^J?V*4#[)P<LUU3PS"40[/DIATX
MO#T]OBSK%M9G4E[C]"M;0:>(&W:9_-IL[W</3-85ORLX+^IVQUO1W I>O\^N
M/_RNPBX8N[?_V/@B*#OX=1?R"U!+ P04    " #-A'I/F5R<(Q &  "<)P
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M])=9SUZ",*V'O\E$_37 ZM=E7U!+ P04    " #-A'I/"X_8 R$!  !7!
M$P   %M#;VYT96YT7U1Y<&5S72YX;6RU5$U/PS ,_2M3KVC-X, !;;L 5Y@$
M?R D;ALU7[*]T?U[W&Y#8BIB:-LEB?/L]UX2*_/W;0::=,%'6A0-<WY0BDP#
M05.9,D1!JH1!LX18JZQ-JVM0=[/9O3(I,D2><L]1+.=/4.FUY\GC;K^G7A0Z
M9^^,9I>BVD1[1#K=$Y8(?LBAQF6ZD81B\MP)"\G>HA"4"G6"PG%A'TO=ZP80
MG85_64M5Y0S89-9!2DK*"-I2 \#!E]1H!/O&Z&*]][O2R"\Z"+'JO/J14%[/
M!V\]C!L8D$LJL[0%C$D-P&Z\/4OPT TF(4PS"HKL1HXGEE:"DNH3+WE$Z%O'
M@CU)7*BO]["?"=MA/7;AWR"I83KOUO]H=)G+H%W\S<A'2NU!7PW_R?(+4$L!
M A0#%     @ S81Z3Q\CSP/     $P(   L              ( !     %]R
M96QS+RYR96QS4$L! A0#%     @ S81Z3R?HAPZ"    L0   !
M     ( !Z0   &1O8U!R;W!S+V%P<"YX;6Q02P$"% ,4    " #-A'I/ 2_N
MG.X    K @  $0              @ &9 0  9&]C4')O<',O8V]R92YX;6Q0
M2P$"% ,4    " #-A'I/F5R<(Q &  "<)P  $P              @ &V @
M>&PO=&AE;64O=&AE;64Q+GAM;%!+ 0(4 Q0    ( ,V$>D\P7#(!E@,  $\4
M   8              "  ?<(  !X;"]W;W)K<VAE971S+W-H965T,2YX;6Q0
M2P$"% ,4    " #-A'I/@Q^AB\H"   W"0  %               @ '##
M>&PO<VAA<F5D4W1R:6YG<RYX;6Q02P$"% ,4    " #-A'I/3+?[F,X!  "
M!0  #0              @ &_#P  >&PO<W1Y;&5S+GAM;%!+ 0(4 Q0    (
M ,V$>D\6;2-_0P$  #P"   /              "  ;@1  !X;"]W;W)K8F]O
M:RYX;6Q02P$"% ,4    " #-A'I/_\ F"+T   "% @  &@
M@ $H$P  >&PO7W)E;',O=V]R:V)O;VLN>&UL+G)E;'-02P$"% ,4    " #-
MA'I/"X_8 R$!  !7!   $P              @ $=%   6T-O;G1E;G1?5'EP
=97-=+GAM;%!+!08     "@ * ( "  !O%0     !

end
</TEXT>
</DOCUMENT>
<DOCUMENT>
<TYPE>ZIP
<SEQUENCE>10
<FILENAME>0001560385-19-000034-xbrl.zip
<DESCRIPTION>IDEA: XBRL DOCUMENT
<TEXT>
begin 644 0001560385-19-000034-xbrl.zip
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MM=1L-CM-R[$21YN7@ 36RJM5; ;1<2IMJ+^%!:OUA#(CH]K5!/"Q]B>7XRD
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M71'OBY"K8+1CURQ8Y=(T%M3K4$LR\7=HCF4"GD3?89 '+<^L&<RTE2D>:NM
M-B(N92%E\0#'RH=T22+V-*1>,4%EJR8!K2Y4\0Q;!]^OV$??ESEGU3R374$=
M(<AYL^1*5"D>.U_W*))$.X'KD2=TXAE.?FM1PJS ODD JTA4/$?6L>@&GB>&
MFJ<?4KI=2#+'MDD4=>4IG@1K)#@4ES=L0E?[W\LJ+!M(KT1<RN[LN-C%LF_8
M+:././D1F!+@GGD#*>HH3%'VCN2VF$9^2WD$_?]PJ#R4YADW$&.YONQ9OEW[
M@I0;QX A6,!MN[H9I$H596SJ3[3(7_7ZMPM*BI\/]DV:P4A+5<:I_M3*OZ*S
M"!&945^2]"B\GU\NM&L&,7UI&395>J7S*MCNJ8]='&$ROQ:*&98Q'# [-&H&
M,$U=&2U5LN1U:-TR)&<7$N>B.$TN?ZS.;F:SW&VQV'A7Y<F9<]I[@_0JZLLH
MJG(IM5 <<;Y$K!++@R:-)*JG,N.JRJJ\TEZ*W*7LSW:F$_GB2-Y.NF?2#&Y:
MJC).]>=.)@S*U^CNGX(IS;O=[=0W@U"YI Q/_>F1;#9=K=T%)'-4\"5=GEDS
M8&DKRY@=2Q[D*D!L+J;9-T97T4)LY2$D3X6)D%SK9A"L*C#[@KS^5$@2^5#H
M9= ?B3OO^F]4C'#/KDGP=*1EV.K/D@R$2$\*_>K#>0ZNG?IF8"J7E.'12HZ<
M6_LO&\LWU*UD(''RKNZG_P%02P,$%     @ S81Z3Z\\AB\-"P  O2T  !P
M  !L;6-A+3(P,3DQ,3(V97@Y.3$R-S!C-V,N:'1M[5IM4]M($OZ<J[K_,$=>
MBE3)QC;O-DL5$+*;"Y $2&6_CJ2Q/8ND468D&]^OOZ=G)%DRAH5=DMN]L*E%
MLN:MI_OII[M'VOM7J_7+Y>D)"U60QR+)6* %ST3(IC(;LTN5ICQAIT)K&47L
M4,MP)!C;;6^T.^V=;JNU_\]_[&&*HV*02OJLVUWK;:WU.MU=UNNO;_9[&^SC
MJ>LYSN((UV=[8\%#NGFVE\DL$O;VV?'U6/HR8[N[[:YM6ZL:]];*$7N^"F?[
M>Z&<,)/-(O'32LSU2":M2 RS?K?;WM[:V.YL[NSTUC=[NR\'1:N6H_&RYI7&
M5%,99N-^M]-YN8*UK QI<YE^)\T&F;C.6CR2HZ1OYQT,59*UACR6T:Q_*6-A
MV)F8LG,5\\2SOSTCM!RZ?D;^1T!+-%&YRK,]:BE7"J5)(S[KRR22B:@-*L;4
M];2W1LVEL&NIO=M;PY[VB[^/IZG;],'H_VX/LOE*AT*W?)5E*NYW\3A1B6#/
MW^S2/U97W&^YR>1PYAX5/[#C#,-E*-1(\W3\I_3Z4*V>J8F(?:%9;\MCA-Y*
MM:5B[[G[3*5+MOYW4\U46!SX*@IOZNI$0E'9#+P02LZ.E$Z5YIE4"3N*E($L
M'[6<@!#8A^$04B0CIH;LQ59GO;V]R4[!)-05CWKX_9(=7P=CGHP$]R/!+D0B
ME69OA \RRC7F"G,!>VSLODI\DPY^;*LT:.#LYY/C+Q\^O/%@@4BU6ZW5P\\7
M[\Z.+R[8EW?GQZ];+78#U.QVTZV^>GZ-+D>#HHO[^6;PFJV><1/RKWUV<O'K
MZ8%G+X?N\MYCAP>7YP?N@E]OOWPX.W"7]Z\93Q*5)P$B0Z9"/F/9F&<,U#7F
MA@4$E1"_#$NUF$B5FVA6&Y$6&%)+,!07&.*CD18CVPN\)1,>T; DD"GN>(R9
M,AI&:SBLB3K6C,-:>!O65K.Q8*56YHB<*Z8QVQ!374@M<\-^/66_P'$@M&'O
MDJ ]UZWKX*:O]9I/&:@XQL9,IH(KCTW',A@S;"C*0XCW8GMG^>Z7;;KF0@;+
ML#37)N=HRQ2CC<E$9I(4EFOL K@LI-@=,)5:1*!CV<AX&$IZB 'SB4L!IS"G
MN!8ZD-:B"1OFR!8PPQQ_FPY_[2<O;GKQYY0,F510:EK.8TY?A4MZS+J/*0SD
ML1@^%8I(3L12[#7!1$:?\ @0QYT6Y!<)"[@9PWF8&7-""AXVW']W8$#)6J+I
MJ.(.MQ(6.G+S7]#\-?X@7I@C&I/S9$:R^'#0K*#^^7JWN,2"\%9.GH1K35EI
MJ0)3[N\[A^H(NNIVM]J=7F_[@6LQ=(:>,RW]/+.>#3\0'#!_T?4ZG<[O.)O'
MM "?@5LR(BV8MG2TRL28(+"&YFFJU;6,X<*@OA<[[:V>I1&[FI7ZOD(W='
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M??MZL]J9*!E^WV+'?@5M/WZF3=HOJ?\+4$L#!!0    ( ,V$>D_O8SBD2AD
M /W=   4    ;&UC82TR,#$Y,3$R-G@X:RYH=&WM76=SZCB_?__,W.^@F[V[
MFS-S#&[4Y.09>DAHH:2]880M0,$M+I1\^BO9A@"!  GIWID] 21+_ZZ?_I*E
MX_^.504,D6EA7?OS+Q=B_P5(DW09:[T__[::>2;^[W]/_@/(?^X_ !S_+\,
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M+6\M<CZ0]_O*.Y00 Y'O1^1AVZ0AYTW"R]L,,&NQ)MVW2'. -C),?4@'QK<
MFQX@*6J2;A(8[#;GYI<SNJ/9YB2CRSX^(0-/Q<9]2U(FF9;#3HIP='UV-61'
M;<X=S'9 *%EB?"."G=<"DC>)5N^AOK?13AXKB/1 X/JC*JZYD7R=KDHM?-8>
M1M!E(=?E>T05W!-5+,J>I?M#(P3([$?X.X2NKRO_)AP7_;2^Y#:YI RI8,2O
M\_?-UN#^,I(KJZ?W#[?1%%$&OT$90HSAHHF$F%@__?JB4>UYK.]&&)IDT>T^
MF8/>.2:V9.QE7_3N^^93GU"'YV.A2Z39@QI^<+\OS#B^4)AZ7A]T?1!;=)7Q
M@V5/(QWPO.OEDO[PF/2\L(NA>J@1 CG54/0)78G]4(DO!C90T4-/!>^&G[ [
MJPSR06\QP*1DV426Y?\I$1*YN:30^:UXJU2589N%S( 7RME;OB ^S?<M#BT<
M+[ L\+<"@+0RE$,?G?]Y1TEFR,>JV=1'VIP<A=/\I'97.(VV&J<,Q[>J9T)L
M1=YT48XYK:>@D:[+*Z7WW)._5RUV?1"^]\7BCKM5LT;ZP^X+.C/9E(N::$;&
M*7$PN1LCJ9HX5VZ83;+)Z H9)F5]9]&\QS+@ N,UG0A:N<7&XWS&93N>*<AJ
MO)")Y1I0:I<;6;5UJ8\VL!UG.8[_8&<Z]!FCN5+#),K$!E0 (JISZ+2>_$Q"
M.ND1:C)XP :1KHQ^O1=]@/X?]?X26A^SW?_\)22.+& C!1E]74- <X?ZWX P
MH#ATD 9D6@A=:I/@624<OH,)T2B2(O0L&4TLHHV'B4[VK%55D2!=Z+?7;6N3
MT<1X=F<W^?4>?E+2"9"J46W,SVJ\)?.B96>9SFF6O9=[E43[U&AUHZE-WA&+
M,!&17<WMBPPPL<K^UEI;IH^D@;M*"@T26(EST+E&1Q^##E+T$<#>$BK=>0OB
MS#GH$J1![ Y;Q AMI,E(IFO!%E8=Q88:TAU+F0"+B-GJ3MPG_0?T#C%^?X[@
M-3FW9N&0=DSB?)-I65=72.?T.1KL,87:%CBT$ (%I"&3^&Y1(\\ZWAPH%>)#
M'KF_DON7F>#+C)W);,,@U=%UI0,) S81X_Z'J"L3VT3R=!+B:#X0M>;,\(J)
M7PVZ0S/6FJ3*;%_*63H?N5@%;*>"::">CD"K"!H3E9CEORZ 3,1$\1% +AHF
M\&D@O,T3 8RY[0%UA\Q-1#[B*W=I'9XNOQ]R,9#)UP&!7B%2\87Q]BNIKJ&[
MFT>)79>)EQ%74^;TAF*G@VZZ=7G#\FJ:ZQM7L-N!O?WJ[9$ H/H4/%4:)T*"
M]^;TMK!I8J8UD0UY-7^ XFHFHOY&WUAU]TK1P&=6N]V%^'^64F#$;#6X0<-P
MRNQE]TX:=_?L>(001IJC9*,'<J+,\(>=7]NITZO[\Q1:M"P'F:O5>EXZ;6?.
MSXQ>2[4?SA,%+!9BS(>K54",>"AMIU:_[ONH=5XJHSZV$4-^D5#2("S2]34Z
MGYD;"#P4@$R"(XQ5F\M<V_51 6$L.9_2\#<:")%.C.>(^CE!B+?%*!MI)R)R
MI-V)=B.=>"<JB9*XO"F@<2W8PBF^.<MA;A)-75?UNQ%.M=FG&PVRM\/KDJ'#
M>*MZU;Y%2JU;NFGT_%6<A9KRQ>E0;$]BQ18SB$IXI/8JN#-=9%BHB?A^OG96
M:;*MJVN[H)UGSYU6I>=GP-]R2X*7\TLD0O'$ZMT&XH;=!K9N3!.'B5!B?98V
M$A)]LW _K5Z+%?>Y%OMT 9:>.<$^V6"Q^X1N>1VVN>/R]:/,> )TN!\ALP_=
M+[!@I-%H(/#][A98WE$AAG:.&GY(\_=/<,88$'%@&?S%NO]-R]U0MK;4]#=T
MK"FFDGU:N-84N,C,%NC'-;M'/%Z_B#V\)@NU8#Z+#?$T!M)3?[S=P5(?2/15
M.?!:9_6C8V ;7]TV3.CF)CWTNZ<0'EC%%[>*BK]5V0T8:#I7(1"?3!+(+W/S
M@+<9@U0B> 4%P]#V1A4AEC0[:"[I?B)/HT/V-T.*?KW6X%;.*W>QM]<D^M_C
MR+#YQ)\WX9UP?,<=.6=[H3*P >O5U&6ZI:;[5VRI*_#UZH6_X?UYAVH0MFDF
M=;9J[5-9!AZ%P.4%K$DY[!B/%[PG&*@#[WE/[_$!A8<G9J[#I(N71;8D/PR<
M?$\Y+3.,?-VX\-\ >=YU2HWK<NH-'"/ *M_.,9;STA2YO&SSR0<-.=.T\..K
M<\1W<JF[2U6/5AHMQKH]0Z7A?;5T/GTG:@.Z[R-0@98,[_T!I@S- ;)!J939
MR:,"(!=XW*<9BC:<S;@-DBM<1\[03;QRRE;/LYEQ]/:2R"+5%K9'<NGGD5P
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M82TR,#$Y,3$R-E]D968N>&UL4$L! A0#%     @ S81Z3\7D;%D9"   DVP
M !4              ( !Q@H  &QM8V$M,C Q.3$Q,C9?;&%B+GAM;%!+ 0(4
M Q0    ( ,V$>D_\Q57OP@4  )8_   5              "  1(3  !L;6-A
M+3(P,3DQ,3(V7W!R92YX;6Q02P$"% ,4    " #-A'I/KSR&+PT+  "]+0
M'               @ $'&0  ;&UC82TR,#$Y,3$R-F5X.3DQ,C<P8S=C+FAT
M;5!+ 0(4 Q0    ( ,V$>D_O8SBD2AD  /W=   4              "  4XD
L  !L;6-A+3(P,3DQ,3(V>#AK+FAT;5!+!08     !@ & )0!  #*/0     !

end
</TEXT>
</DOCUMENT>
<DOCUMENT>
<TYPE>XML
<SEQUENCE>11
<FILENAME>Show.js
<DESCRIPTION>IDEA: XBRL DOCUMENT
<TEXT>
// Edgar(tm) Renderer was created by staff of the U.S. Securities and Exchange Commission.  Data and content created by government employees within the scope of their employment are not subject to domestic copyright protection. 17 U.S.C. 105.
var Show={};Show.LastAR=null,Show.showAR=function(a,r,w){if(Show.LastAR)Show.hideAR();var e=a;while(e&&e.nodeName!='TABLE')e=e.nextSibling;if(!e||e.nodeName!='TABLE'){var ref=((window)?w.document:document).getElementById(r);if(ref){e=ref.cloneNode(!0);
e.removeAttribute('id');a.parentNode.appendChild(e)}}
if(e)e.style.display='block';Show.LastAR=e};Show.hideAR=function(){Show.LastAR.style.display='none'};Show.toggleNext=function(a){var e=a;while(e.nodeName!='DIV')e=e.nextSibling;if(!e.style){}else if(!e.style.display){}else{var d,p_;if(e.style.display=='none'){d='block';p='-'}else{d='none';p='+'}
e.style.display=d;if(a.textContent){a.textContent=p+a.textContent.substring(1)}else{a.innerText=p+a.innerText.substring(1)}}}
</TEXT>
</DOCUMENT>
<DOCUMENT>
<TYPE>XML
<SEQUENCE>12
<FILENAME>report.css
<DESCRIPTION>IDEA: XBRL DOCUMENT
<TEXT>
/* Updated 2009-11-04 */
/* v2.2.0.24 */

/* DefRef Styles */
..report table.authRefData{
	background-color: #def;
	border: 2px solid #2F4497;
	font-size: 1em;
	position: absolute;
}

..report table.authRefData a {
	display: block;
	font-weight: bold;
}

..report table.authRefData p {
	margin-top: 0px;
}

..report table.authRefData .hide {
	background-color: #2F4497;
	padding: 1px 3px 0px 0px;
	text-align: right;
}

..report table.authRefData .hide a:hover {
	background-color: #2F4497;
}

..report table.authRefData .body {
	height: 150px;
	overflow: auto;
	width: 400px;
}

..report table.authRefData table{
	font-size: 1em;
}

/* Report Styles */
..pl a, .pl a:visited {
	color: black;
	text-decoration: none;
}

/* table */
..report {
	background-color: white;
	border: 2px solid #acf;
	clear: both;
	color: black;
	font: normal 8pt Helvetica, Arial, san-serif;
	margin-bottom: 2em;
}

..report hr {
	border: 1px solid #acf;
}

/* Top labels */
..report th {
	background-color: #acf;
	color: black;
	font-weight: bold;
	text-align: center;
}

..report th.void	{
	background-color: transparent;
	color: #000000;
	font: bold 10pt Helvetica, Arial, san-serif;
	text-align: left;
}

..report .pl {
	text-align: left;
	vertical-align: top;
	white-space: normal;
	width: 200px;
	white-space: normal; /* word-wrap: break-word; */
}

..report td.pl a.a {
	cursor: pointer;
	display: block;
	width: 200px;
	overflow: hidden;
}

..report td.pl div.a {
	width: 200px;
}

..report td.pl a:hover {
	background-color: #ffc;
}

/* Header rows... */
..report tr.rh {
	background-color: #acf;
	color: black;
	font-weight: bold;
}

/* Calendars... */
..report .rc {
	background-color: #f0f0f0;
}

/* Even rows... */
..report .re, .report .reu {
	background-color: #def;
}

..report .reu td {
	border-bottom: 1px solid black;
}

/* Odd rows... */
..report .ro, .report .rou {
	background-color: white;
}

..report .rou td {
	border-bottom: 1px solid black;
}

..report .rou table td, .report .reu table td {
	border-bottom: 0px solid black;
}

/* styles for footnote marker */
..report .fn {
	white-space: nowrap;
}

/* styles for numeric types */
..report .num, .report .nump {
	text-align: right;
	white-space: nowrap;
}

..report .nump {
	padding-left: 2em;
}

..report .nump {
	padding: 0px 0.4em 0px 2em;
}

/* styles for text types */
..report .text {
	text-align: left;
	white-space: normal;
}

..report .text .big {
	margin-bottom: 1em;
	width: 17em;
}

..report .text .more {
	display: none;
}

..report .text .note {
	font-style: italic;
	font-weight: bold;
}

..report .text .small {
	width: 10em;
}

..report sup {
	font-style: italic;
}

..report .outerFootnotes {
	font-size: 1em;
}
</TEXT>
</DOCUMENT>
<DOCUMENT>
<TYPE>XML
<SEQUENCE>13
<FILENAME>lmca-20191126x8k_htm.xml
<DESCRIPTION>IDEA: XBRL DOCUMENT
<TEXT>
<XML>
<?xml version="1.0" encoding="utf-8"?>
<xbrl
  xmlns="http://www.xbrl.org/2003/instance"
  xmlns:dei="http://xbrl.sec.gov/dei/2019-01-31"
  xmlns:link="http://www.xbrl.org/2003/linkbase"
  xmlns:lmca="http://www.libertymedia.com/20191126"
  xmlns:us-gaap="http://fasb.org/us-gaap/2019-01-31"
  xmlns:xbrldi="http://xbrl.org/2006/xbrldi"
  xmlns:xlink="http://www.w3.org/1999/xlink">
    <link:schemaRef xlink:href="lmca-20191126.xsd" xlink:type="simple"/>
    <context id="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertySiriusXmGroupCommonClassMember_JAHYIrCcpE2AfOW2m-MR9A">
        <entity>
            <identifier scheme="http://www.sec.gov/CIK">0001560385</identifier>
            <segment>
                <xbrldi:explicitMember dimension="us-gaap:StatementClassOfStockAxis">lmca:LibertySiriusXmGroupCommonClassMember</xbrldi:explicitMember>
            </segment>
        </entity>
        <period>
            <startDate>2019-11-26</startDate>
            <endDate>2019-11-26</endDate>
        </period>
    </context>
    <context id="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertySiriusXmGroupCommonClassCMember_8SciBKG1tUqNetlucqFpLg">
        <entity>
            <identifier scheme="http://www.sec.gov/CIK">0001560385</identifier>
            <segment>
                <xbrldi:explicitMember dimension="us-gaap:StatementClassOfStockAxis">lmca:LibertySiriusXmGroupCommonClassCMember</xbrldi:explicitMember>
            </segment>
        </entity>
        <period>
            <startDate>2019-11-26</startDate>
            <endDate>2019-11-26</endDate>
        </period>
    </context>
    <context id="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertySiriusXmGroupCommonClassBMember_LcWuYDm5dEWjseRLNV3nmw">
        <entity>
            <identifier scheme="http://www.sec.gov/CIK">0001560385</identifier>
            <segment>
                <xbrldi:explicitMember dimension="us-gaap:StatementClassOfStockAxis">lmca:LibertySiriusXmGroupCommonClassBMember</xbrldi:explicitMember>
            </segment>
        </entity>
        <period>
            <startDate>2019-11-26</startDate>
            <endDate>2019-11-26</endDate>
        </period>
    </context>
    <context id="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertyFormulaOneGroupCommonClassMember_Qku0i7SRpkKw2eLBgjmTUQ">
        <entity>
            <identifier scheme="http://www.sec.gov/CIK">0001560385</identifier>
            <segment>
                <xbrldi:explicitMember dimension="us-gaap:StatementClassOfStockAxis">lmca:LibertyFormulaOneGroupCommonClassMember</xbrldi:explicitMember>
            </segment>
        </entity>
        <period>
            <startDate>2019-11-26</startDate>
            <endDate>2019-11-26</endDate>
        </period>
    </context>
    <context id="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertyFormulaOneGroupCommonClassCMember__WB6cnMrU0Cgg-JQk9l8tA">
        <entity>
            <identifier scheme="http://www.sec.gov/CIK">0001560385</identifier>
            <segment>
                <xbrldi:explicitMember dimension="us-gaap:StatementClassOfStockAxis">lmca:LibertyFormulaOneGroupCommonClassCMember</xbrldi:explicitMember>
            </segment>
        </entity>
        <period>
            <startDate>2019-11-26</startDate>
            <endDate>2019-11-26</endDate>
        </period>
    </context>
    <context id="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertyBravesGroupCommonClassMember_sm1mh0XjS0-TMjZL81FbQA">
        <entity>
            <identifier scheme="http://www.sec.gov/CIK">0001560385</identifier>
            <segment>
                <xbrldi:explicitMember dimension="us-gaap:StatementClassOfStockAxis">lmca:LibertyBravesGroupCommonClassMember</xbrldi:explicitMember>
            </segment>
        </entity>
        <period>
            <startDate>2019-11-26</startDate>
            <endDate>2019-11-26</endDate>
        </period>
    </context>
    <context id="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertyBravesGroupCommonClassCMember_Fl2DCyEN9Uuz5sCVzEVhwQ">
        <entity>
            <identifier scheme="http://www.sec.gov/CIK">0001560385</identifier>
            <segment>
                <xbrldi:explicitMember dimension="us-gaap:StatementClassOfStockAxis">lmca:LibertyBravesGroupCommonClassCMember</xbrldi:explicitMember>
            </segment>
        </entity>
        <period>
            <startDate>2019-11-26</startDate>
            <endDate>2019-11-26</endDate>
        </period>
    </context>
    <context id="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA">
        <entity>
            <identifier scheme="http://www.sec.gov/CIK">0001560385</identifier>
        </entity>
        <period>
            <startDate>2019-11-26</startDate>
            <endDate>2019-11-26</endDate>
        </period>
    </context>
    <dei:EntityCentralIndexKey
      contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA"
      id="Hidden_hjsUU4QfPUWwJcn7bkI3sw">0001560385</dei:EntityCentralIndexKey>
    <dei:AmendmentFlag
      contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA"
      id="Hidden_P2gAuOCpCESucPhx-0_XoQ">false</dei:AmendmentFlag>
    <dei:DocumentType
      contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA"
      id="Narr_RKB_PDK8ZUmQPPvUmWvCoQ">8-K</dei:DocumentType>
    <dei:DocumentPeriodEndDate
      contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA"
      id="Narr_DLyQyteFGkGM4fFBiu3DLQ">2019-11-26</dei:DocumentPeriodEndDate>
    <dei:EntityRegistrantName
      contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA"
      id="Narr_Y66LzTeHvEa0pQfdBDlqog">LIBERTY MEDIA CORPORATION</dei:EntityRegistrantName>
    <dei:EntityIncorporationStateCountryCode
      contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA"
      id="Tc_NtihsclyCUu0yIawXJWv0w_1_0">DE</dei:EntityIncorporationStateCountryCode>
    <dei:EntityFileNumber
      contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA"
      id="Tc_NX1wdXBOcUiJ_v5eVGEf2g_1_1">001-35707</dei:EntityFileNumber>
    <dei:EntityTaxIdentificationNumber
      contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA"
      id="Tc_cGp8XFqTUkqV5EMmHqzZ6A_1_2">37-1699499</dei:EntityTaxIdentificationNumber>
    <dei:EntityAddressAddressLine1
      contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA"
      id="Narr_KZ4ZlOlv_0a-k23MDZ2G4Q">12300 Liberty Blvd.</dei:EntityAddressAddressLine1>
    <dei:EntityAddressCityOrTown
      contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA"
      id="Narr_3HFyPjGH6USH-12UOJ37LQ">Englewood</dei:EntityAddressCityOrTown>
    <dei:EntityAddressStateOrProvince
      contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA"
      id="Narr_MIn4r5xA4kyjxecO9KlY-Q">CO</dei:EntityAddressStateOrProvince>
    <dei:EntityAddressPostalZipCode
      contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA"
      id="Narr_8CGdm8GC7ESac_MSDmUVow">80112</dei:EntityAddressPostalZipCode>
    <dei:CityAreaCode
      contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA"
      id="Narr_75nxv9bDJUOme3cQoZX_sw">720</dei:CityAreaCode>
    <dei:LocalPhoneNumber
      contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA"
      id="Narr_RIstD-bHD0qdgN9_HpUf6A">875-5400</dei:LocalPhoneNumber>
    <dei:WrittenCommunications
      contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA"
      id="Narr_W-8Wkfvr7UyAM0hcEso25Q">false</dei:WrittenCommunications>
    <dei:SolicitingMaterial
      contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA"
      id="Narr_e7HkfBUVY02mB1hpWafbag">false</dei:SolicitingMaterial>
    <dei:PreCommencementTenderOffer
      contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA"
      id="Narr_JAla5rUS1kSpuM0VfjcxfQ">false</dei:PreCommencementTenderOffer>
    <dei:PreCommencementIssuerTenderOffer
      contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA"
      id="Narr_KLH_CKJpgUmtzK9Gi4G7-Q">false</dei:PreCommencementIssuerTenderOffer>
    <dei:Security12bTitle
      contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertySiriusXmGroupCommonClassMember_JAHYIrCcpE2AfOW2m-MR9A"
      id="Tc_CaSaROAVBUmBhW0Lf32ROQ_2_0">Series A Liberty SiriusXM Common Stock </dei:Security12bTitle>
    <dei:TradingSymbol
      contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertySiriusXmGroupCommonClassMember_JAHYIrCcpE2AfOW2m-MR9A"
      id="Tc_-BIVI0LdzkuFglHM--dXSQ_2_1">LSXMA</dei:TradingSymbol>
    <dei:SecurityExchangeName
      contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertySiriusXmGroupCommonClassMember_JAHYIrCcpE2AfOW2m-MR9A"
      id="Tc_EAjVmo6NSU-sZJeLvqOLKg_2_2">NASDAQ</dei:SecurityExchangeName>
    <dei:Security12bTitle
      contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertySiriusXmGroupCommonClassBMember_LcWuYDm5dEWjseRLNV3nmw"
      id="Tc_GX5JeY8NH0OKDCx6ZVghtA_3_0">Series B Liberty SiriusXM Common Stock</dei:Security12bTitle>
    <dei:TradingSymbol
      contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertySiriusXmGroupCommonClassBMember_LcWuYDm5dEWjseRLNV3nmw"
      id="Tc_RSiz3YNm-ECVXDyNIFktMQ_3_1">LSXMB</dei:TradingSymbol>
    <dei:SecurityExchangeName
      contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertySiriusXmGroupCommonClassBMember_LcWuYDm5dEWjseRLNV3nmw"
      id="Tc_-ie0s4MQ-EaNii9Yv-R8mw_3_2">NASDAQ</dei:SecurityExchangeName>
    <dei:Security12bTitle
      contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertySiriusXmGroupCommonClassCMember_8SciBKG1tUqNetlucqFpLg"
      id="Tc_mQidI1qm4EiyG1QXy3aR3Q_4_0">Series C Liberty SiriusXM Common Stock</dei:Security12bTitle>
    <dei:TradingSymbol
      contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertySiriusXmGroupCommonClassCMember_8SciBKG1tUqNetlucqFpLg"
      id="Tc_OIuXa3V04kWLW4OhogrQeA_4_1">LSXMK</dei:TradingSymbol>
    <dei:SecurityExchangeName
      contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertySiriusXmGroupCommonClassCMember_8SciBKG1tUqNetlucqFpLg"
      id="Tc_7wclcA0jUky_2JrypKKiPA_4_2">NASDAQ</dei:SecurityExchangeName>
    <dei:Security12bTitle
      contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertyBravesGroupCommonClassMember_sm1mh0XjS0-TMjZL81FbQA"
      id="Tc_X65jgshJZUGMTaRjNs9Mlg_5_0">Series A Liberty Braves Common Stock</dei:Security12bTitle>
    <dei:TradingSymbol
      contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertyBravesGroupCommonClassMember_sm1mh0XjS0-TMjZL81FbQA"
      id="Tc_NdL2aG1hiUyi2dmkZFZS7Q_5_1">BATRA</dei:TradingSymbol>
    <dei:SecurityExchangeName
      contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertyBravesGroupCommonClassMember_sm1mh0XjS0-TMjZL81FbQA"
      id="Tc_Zg4loVvzVUueA64cIVD5wQ_5_2">NASDAQ</dei:SecurityExchangeName>
    <dei:Security12bTitle
      contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertyBravesGroupCommonClassCMember_Fl2DCyEN9Uuz5sCVzEVhwQ"
      id="Tc_H8deX0NCnEmDwoFls9AH6w_6_0">Series C Liberty Braves Common Stock</dei:Security12bTitle>
    <dei:TradingSymbol
      contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertyBravesGroupCommonClassCMember_Fl2DCyEN9Uuz5sCVzEVhwQ"
      id="Tc_ZpOqEkVcSkCC7Eg7cb-NmA_6_1">BATRK</dei:TradingSymbol>
    <dei:SecurityExchangeName
      contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertyBravesGroupCommonClassCMember_Fl2DCyEN9Uuz5sCVzEVhwQ"
      id="Tc_yGcFo4HizkiZU79wBY08rg_6_2">NASDAQ</dei:SecurityExchangeName>
    <dei:Security12bTitle
      contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertyFormulaOneGroupCommonClassMember_Qku0i7SRpkKw2eLBgjmTUQ"
      id="Tc_Dqqd6NMBkkmMXeqFIZzztA_7_0">Series A Liberty Formula One Common Stock</dei:Security12bTitle>
    <dei:TradingSymbol
      contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertyFormulaOneGroupCommonClassMember_Qku0i7SRpkKw2eLBgjmTUQ"
      id="Tc_walnK5cQcUSXSZo0c4EcqA_7_1">FWONA</dei:TradingSymbol>
    <dei:SecurityExchangeName
      contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertyFormulaOneGroupCommonClassMember_Qku0i7SRpkKw2eLBgjmTUQ"
      id="Tc_F2wwLUlZAkSmm0uQZrcLvA_7_2">NASDAQ</dei:SecurityExchangeName>
    <dei:Security12bTitle
      contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertyFormulaOneGroupCommonClassCMember__WB6cnMrU0Cgg-JQk9l8tA"
      id="Tc_myIN4vsr2kKIt4XeWscPGQ_8_0">Series C Liberty Formula One Common Stock</dei:Security12bTitle>
    <dei:TradingSymbol
      contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertyFormulaOneGroupCommonClassCMember__WB6cnMrU0Cgg-JQk9l8tA"
      id="Tc_rgSEMIZV6UKSDKi2GKAr-g_8_1">FWONK</dei:TradingSymbol>
    <dei:SecurityExchangeName
      contextRef="Duration_11_26_2019_To_11_26_2019_us-gaap_StatementClassOfStockAxis_lmca_LibertyFormulaOneGroupCommonClassCMember__WB6cnMrU0Cgg-JQk9l8tA"
      id="Tc_cHmpG3UWtUaFbtx1WvGMSA_8_2">NASDAQ</dei:SecurityExchangeName>
    <dei:EntityEmergingGrowthCompany
      contextRef="Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA"
      id="Narr_nzq0eq_br0SUgGod7xseWg">false</dei:EntityEmergingGrowthCompany>
</xbrl>
</XML>
</TEXT>
</DOCUMENT>
<DOCUMENT>
<TYPE>XML
<SEQUENCE>14
<FILENAME>FilingSummary.xml
<DESCRIPTION>IDEA: XBRL DOCUMENT
<TEXT>
<XML>
<?xml version='1.0' encoding='utf-8'?>
<FilingSummary>
  <Version>3.19.3</Version>
  <ProcessingTime/>
  <ReportFormat>html</ReportFormat>
  <ContextCount>8</ContextCount>
  <ElementCount>97</ElementCount>
  <EntityCount>1</EntityCount>
  <FootnotesReported>false</FootnotesReported>
  <SegmentCount>7</SegmentCount>
  <ScenarioCount>0</ScenarioCount>
  <TuplesReported>false</TuplesReported>
  <UnitCount>0</UnitCount>
  <MyReports>
    <Report instance="lmca-20191126x8k.htm">
      <IsDefault>false</IsDefault>
      <HasEmbeddedReports>false</HasEmbeddedReports>
      <HtmlFileName>R1.htm</HtmlFileName>
      <LongName>00090 - Document - Document and Entity Information</LongName>
      <ReportType>Sheet</ReportType>
      <Role>http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation</Role>
      <ShortName>Document and Entity Information</ShortName>
      <MenuCategory>Cover</MenuCategory>
      <Position>1</Position>
    </Report>
    <Report>
      <IsDefault>false</IsDefault>
      <HasEmbeddedReports>false</HasEmbeddedReports>
      <LongName>All Reports</LongName>
      <ReportType>Book</ReportType>
      <ShortName>All Reports</ShortName>
    </Report>
  </MyReports>
  <InputFiles>
    <File doctype="8-K" original="lmca-20191126x8k.htm">lmca-20191126x8k.htm</File>
    <File>lmca-20191126.xsd</File>
    <File>lmca-20191126_def.xml</File>
    <File>lmca-20191126_lab.xml</File>
    <File>lmca-20191126_pre.xml</File>
    <File>lmca-20191126ex991270c7c.htm</File>
  </InputFiles>
  <SupplementalFiles/>
  <BaseTaxonomies>
    <BaseTaxonomy>http://fasb.org/us-gaap/2019-01-31</BaseTaxonomy>
    <BaseTaxonomy>http://xbrl.sec.gov/dei/2019-01-31</BaseTaxonomy>
  </BaseTaxonomies>
  <HasPresentationLinkbase>true</HasPresentationLinkbase>
  <HasCalculationLinkbase>false</HasCalculationLinkbase>
</FilingSummary>
</XML>
</TEXT>
</DOCUMENT>
<DOCUMENT>
<TYPE>JSON
<SEQUENCE>15
<FILENAME>MetaLinks.json
<DESCRIPTION>IDEA: XBRL DOCUMENT
<TEXT>
{
 "instance": {
  "lmca-20191126x8k.htm": {
   "axisCustom": 0,
   "axisStandard": 1,
   "contextCount": 8,
   "dts": {
    "definitionLink": {
     "local": [
      "lmca-20191126_def.xml"
     ],
     "remote": [
      "http://xbrl.fasb.org/us-gaap/2019/elts/us-gaap-eedm-def-2019-01-31.xml",
      "http://xbrl.fasb.org/srt/2019/elts/srt-eedm1-def-2019-01-31.xml"
     ]
    },
    "inline": {
     "local": [
      "lmca-20191126x8k.htm"
     ]
    },
    "labelLink": {
     "local": [
      "lmca-20191126_lab.xml"
     ],
     "remote": [
      "https://xbrl.sec.gov/dei/2019/dei-doc-2019-01-31.xml"
     ]
    },
    "presentationLink": {
     "local": [
      "lmca-20191126_pre.xml"
     ]
    },
    "referenceLink": {
     "remote": [
      "https://xbrl.sec.gov/dei/2019/dei-ref-2019-01-31.xml"
     ]
    },
    "schema": {
     "local": [
      "lmca-20191126.xsd"
     ],
     "remote": [
      "http://www.xbrl.org/2003/xbrl-linkbase-2003-12-31.xsd",
      "http://www.xbrl.org/2003/xl-2003-12-31.xsd",
      "http://www.xbrl.org/2003/xlink-2003-12-31.xsd",
      "http://xbrl.fasb.org/us-gaap/2019/elts/us-gaap-2019-01-31.xsd",
      "http://xbrl.fasb.org/us-gaap/2019/elts/us-roles-2019-01-31.xsd",
      "http://www.xbrl.org/2005/xbrldt-2005.xsd",
      "http://www.xbrl.org/2003/xbrl-instance-2003-12-31.xsd",
      "http://xbrl.fasb.org/srt/2019/elts/srt-2019-01-31.xsd",
      "http://www.xbrl.org/dtr/type/numeric-2009-12-16.xsd",
      "http://www.xbrl.org/dtr/type/nonNumeric-2009-12-16.xsd",
      "http://www.xbrl.org/2006/ref-2006-02-27.xsd",
      "http://xbrl.fasb.org/srt/2019/elts/srt-types-2019-01-31.xsd",
      "http://xbrl.fasb.org/srt/2019/elts/srt-roles-2019-01-31.xsd",
      "https://xbrl.sec.gov/country/2017/country-2017-01-31.xsd",
      "http://xbrl.fasb.org/us-gaap/2019/elts/us-types-2019-01-31.xsd",
      "https://xbrl.sec.gov/dei/2019/dei-2019-01-31.xsd",
      "http://www.xbrl.org/lrr/role/deprecated-2009-12-16.xsd"
     ]
    }
   },
   "elementCount": 34,
   "entityCount": 1,
   "hidden": {
    "http://xbrl.sec.gov/dei/2019-01-31": 2,
    "total": 2
   },
   "keyCustom": 0,
   "keyStandard": 97,
   "memberCustom": 7,
   "memberStandard": 0,
   "nsprefix": "lmca",
   "nsuri": "http://www.libertymedia.com/20191126",
   "report": {
    "R1": {
     "firstAnchor": {
      "ancestors": [
       "p",
       "div",
       "div",
       "body",
       "html"
      ],
      "baseRef": "lmca-20191126x8k.htm",
      "contextRef": "Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA",
      "decimals": null,
      "first": true,
      "lang": "en-US",
      "name": "dei:DocumentType",
      "reportCount": 1,
      "unique": true,
      "unitRef": null,
      "xsiNil": "false"
     },
     "groupType": "document",
     "isDefault": "true",
     "longName": "00090 - Document - Document and Entity Information",
     "role": "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation",
     "shortName": "Document and Entity Information",
     "subGroupType": "",
     "uniqueAnchor": {
      "ancestors": [
       "p",
       "div",
       "div",
       "body",
       "html"
      ],
      "baseRef": "lmca-20191126x8k.htm",
      "contextRef": "Duration_11_26_2019_To_11_26_2019_P9ECMo7GkUiw1MQwoAa7gA",
      "decimals": null,
      "first": true,
      "lang": "en-US",
      "name": "dei:DocumentType",
      "reportCount": 1,
      "unique": true,
      "unitRef": null,
      "xsiNil": "false"
     }
    }
   },
   "segmentCount": 7,
   "tag": {
    "dei_AmendmentFlag": {
     "auth_ref": [],
     "lang": {
      "en-US": {
       "role": {
        "documentation": "Boolean flag that is true when the XBRL content amends previously-filed or accepted submission.",
        "label": "Amendment Flag"
       }
      }
     },
     "localname": "AmendmentFlag",
     "nsuri": "http://xbrl.sec.gov/dei/2019-01-31",
     "presentation": [
      "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation"
     ],
     "xbrltype": "booleanItemType"
    },
    "dei_CityAreaCode": {
     "auth_ref": [],
     "lang": {
      "en-US": {
       "role": {
        "documentation": "Area code of city",
        "label": "City Area Code"
       }
      }
     },
     "localname": "CityAreaCode",
     "nsuri": "http://xbrl.sec.gov/dei/2019-01-31",
     "presentation": [
      "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation"
     ],
     "xbrltype": "normalizedStringItemType"
    },
    "dei_DocumentPeriodEndDate": {
     "auth_ref": [],
     "lang": {
      "en-US": {
       "role": {
        "documentation": "The end date of the period reflected on the cover page if a periodic report. For all other reports and registration statements containing historical data, it is the date up through which that historical data is presented.  If there is no historical data in the report, use the filing date. The format of the date is CCYY-MM-DD.",
        "label": "Document Period End Date"
       }
      }
     },
     "localname": "DocumentPeriodEndDate",
     "nsuri": "http://xbrl.sec.gov/dei/2019-01-31",
     "presentation": [
      "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation"
     ],
     "xbrltype": "dateItemType"
    },
    "dei_DocumentType": {
     "auth_ref": [],
     "lang": {
      "en-US": {
       "role": {
        "documentation": "The type of document being provided (such as 10-K, 10-Q, 485BPOS, etc). The document type is limited to the same value as the supporting SEC submission type, or the word 'Other'.",
        "label": "Document Type"
       }
      }
     },
     "localname": "DocumentType",
     "nsuri": "http://xbrl.sec.gov/dei/2019-01-31",
     "presentation": [
      "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation"
     ],
     "xbrltype": "submissionTypeItemType"
    },
    "dei_EntityAddressAddressLine1": {
     "auth_ref": [],
     "lang": {
      "en-US": {
       "role": {
        "documentation": "Address Line 1 such as Attn, Building Name, Street Name",
        "label": "Entity Address, Address Line One"
       }
      }
     },
     "localname": "EntityAddressAddressLine1",
     "nsuri": "http://xbrl.sec.gov/dei/2019-01-31",
     "presentation": [
      "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation"
     ],
     "xbrltype": "normalizedStringItemType"
    },
    "dei_EntityAddressCityOrTown": {
     "auth_ref": [],
     "lang": {
      "en-US": {
       "role": {
        "documentation": "Name of the City or Town",
        "label": "Entity Address, City or Town"
       }
      }
     },
     "localname": "EntityAddressCityOrTown",
     "nsuri": "http://xbrl.sec.gov/dei/2019-01-31",
     "presentation": [
      "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation"
     ],
     "xbrltype": "normalizedStringItemType"
    },
    "dei_EntityAddressPostalZipCode": {
     "auth_ref": [],
     "lang": {
      "en-US": {
       "role": {
        "documentation": "Code for the postal or zip code",
        "label": "Entity Address, Postal Zip Code"
       }
      }
     },
     "localname": "EntityAddressPostalZipCode",
     "nsuri": "http://xbrl.sec.gov/dei/2019-01-31",
     "presentation": [
      "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation"
     ],
     "xbrltype": "normalizedStringItemType"
    },
    "dei_EntityAddressStateOrProvince": {
     "auth_ref": [],
     "lang": {
      "en-US": {
       "role": {
        "documentation": "Name of the state or province.",
        "label": "Entity Address, State or Province"
       }
      }
     },
     "localname": "EntityAddressStateOrProvince",
     "nsuri": "http://xbrl.sec.gov/dei/2019-01-31",
     "presentation": [
      "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation"
     ],
     "xbrltype": "stateOrProvinceItemType"
    },
    "dei_EntityCentralIndexKey": {
     "auth_ref": [
      "r5"
     ],
     "lang": {
      "en-US": {
       "role": {
        "documentation": "A unique 10-digit SEC-issued value to identify entities that have filed disclosures with the SEC. It is commonly abbreviated as CIK.",
        "label": "Entity Central Index Key"
       }
      }
     },
     "localname": "EntityCentralIndexKey",
     "nsuri": "http://xbrl.sec.gov/dei/2019-01-31",
     "presentation": [
      "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation"
     ],
     "xbrltype": "centralIndexKeyItemType"
    },
    "dei_EntityEmergingGrowthCompany": {
     "auth_ref": [
      "r5"
     ],
     "lang": {
      "en-US": {
       "role": {
        "documentation": "Indicate if registrant meets the emerging growth company criteria.",
        "label": "Entity Emerging Growth Company"
       }
      }
     },
     "localname": "EntityEmergingGrowthCompany",
     "nsuri": "http://xbrl.sec.gov/dei/2019-01-31",
     "presentation": [
      "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation"
     ],
     "xbrltype": "booleanItemType"
    },
    "dei_EntityFileNumber": {
     "auth_ref": [],
     "lang": {
      "en-US": {
       "role": {
        "documentation": "Commission file number. The field allows up to 17 characters. The prefix may contain 1-3 digits, the sequence number may contain 1-8 digits, the optional suffix may contain 1-4 characters, and the fields are separated with a hyphen.",
        "label": "Entity File Number"
       }
      }
     },
     "localname": "EntityFileNumber",
     "nsuri": "http://xbrl.sec.gov/dei/2019-01-31",
     "presentation": [
      "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation"
     ],
     "xbrltype": "fileNumberItemType"
    },
    "dei_EntityIncorporationStateCountryCode": {
     "auth_ref": [],
     "lang": {
      "en-US": {
       "role": {
        "documentation": "Two-character EDGAR code representing the state or country of incorporation.",
        "label": "Entity Incorporation, State or Country Code"
       }
      }
     },
     "localname": "EntityIncorporationStateCountryCode",
     "nsuri": "http://xbrl.sec.gov/dei/2019-01-31",
     "presentation": [
      "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation"
     ],
     "xbrltype": "edgarStateCountryItemType"
    },
    "dei_EntityRegistrantName": {
     "auth_ref": [
      "r5"
     ],
     "lang": {
      "en-US": {
       "role": {
        "documentation": "The exact name of the entity filing the report as specified in its charter, which is required by forms filed with the SEC.",
        "label": "Entity Registrant Name"
       }
      }
     },
     "localname": "EntityRegistrantName",
     "nsuri": "http://xbrl.sec.gov/dei/2019-01-31",
     "presentation": [
      "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation"
     ],
     "xbrltype": "normalizedStringItemType"
    },
    "dei_EntityTaxIdentificationNumber": {
     "auth_ref": [
      "r5"
     ],
     "lang": {
      "en-US": {
       "role": {
        "documentation": "The Tax Identification Number (TIN), also known as an Employer Identification Number (EIN), is a unique 9-digit value assigned by the IRS.",
        "label": "Entity Tax Identification Number"
       }
      }
     },
     "localname": "EntityTaxIdentificationNumber",
     "nsuri": "http://xbrl.sec.gov/dei/2019-01-31",
     "presentation": [
      "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation"
     ],
     "xbrltype": "employerIdItemType"
    },
    "dei_LocalPhoneNumber": {
     "auth_ref": [],
     "lang": {
      "en-US": {
       "role": {
        "documentation": "Local phone number for entity.",
        "label": "Local Phone Number"
       }
      }
     },
     "localname": "LocalPhoneNumber",
     "nsuri": "http://xbrl.sec.gov/dei/2019-01-31",
     "presentation": [
      "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation"
     ],
     "xbrltype": "normalizedStringItemType"
    },
    "dei_PreCommencementIssuerTenderOffer": {
     "auth_ref": [
      "r2"
     ],
     "lang": {
      "en-US": {
       "role": {
        "documentation": "Boolean flag that is true when the Form 8-K filing is intended to satisfy the filing obligation of the registrant as pre-commencement communications pursuant to Rule 13e-4(c) under the Exchange Act.",
        "label": "Pre-commencement Issuer Tender Offer"
       }
      }
     },
     "localname": "PreCommencementIssuerTenderOffer",
     "nsuri": "http://xbrl.sec.gov/dei/2019-01-31",
     "presentation": [
      "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation"
     ],
     "xbrltype": "booleanItemType"
    },
    "dei_PreCommencementTenderOffer": {
     "auth_ref": [
      "r3"
     ],
     "lang": {
      "en-US": {
       "role": {
        "documentation": "Boolean flag that is true when the Form 8-K filing is intended to satisfy the filing obligation of the registrant as pre-commencement communications pursuant to Rule 14d-2(b) under the Exchange Act.",
        "label": "Pre-commencement Tender Offer"
       }
      }
     },
     "localname": "PreCommencementTenderOffer",
     "nsuri": "http://xbrl.sec.gov/dei/2019-01-31",
     "presentation": [
      "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation"
     ],
     "xbrltype": "booleanItemType"
    },
    "dei_Security12bTitle": {
     "auth_ref": [
      "r0"
     ],
     "lang": {
      "en-US": {
       "role": {
        "documentation": "Title of a 12(b) registered security.",
        "label": "Title of 12(b) Security"
       }
      }
     },
     "localname": "Security12bTitle",
     "nsuri": "http://xbrl.sec.gov/dei/2019-01-31",
     "presentation": [
      "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation"
     ],
     "xbrltype": "securityTitleItemType"
    },
    "dei_SecurityExchangeName": {
     "auth_ref": [
      "r1"
     ],
     "lang": {
      "en-US": {
       "role": {
        "documentation": "Name of the Exchange on which a security is registered.",
        "label": "Security Exchange Name"
       }
      }
     },
     "localname": "SecurityExchangeName",
     "nsuri": "http://xbrl.sec.gov/dei/2019-01-31",
     "presentation": [
      "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation"
     ],
     "xbrltype": "edgarExchangeCodeItemType"
    },
    "dei_SolicitingMaterial": {
     "auth_ref": [
      "r4"
     ],
     "lang": {
      "en-US": {
       "role": {
        "documentation": "Boolean flag that is true when the Form 8-K filing is intended to satisfy the filing obligation of the registrant as soliciting material pursuant to Rule 14a-12 under the Exchange Act.",
        "label": "Soliciting Material"
       }
      }
     },
     "localname": "SolicitingMaterial",
     "nsuri": "http://xbrl.sec.gov/dei/2019-01-31",
     "presentation": [
      "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation"
     ],
     "xbrltype": "booleanItemType"
    },
    "dei_TradingSymbol": {
     "auth_ref": [],
     "lang": {
      "en-US": {
       "role": {
        "documentation": "Trading symbol of an instrument as listed on an exchange.",
        "label": "Trading Symbol"
       }
      }
     },
     "localname": "TradingSymbol",
     "nsuri": "http://xbrl.sec.gov/dei/2019-01-31",
     "presentation": [
      "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation"
     ],
     "xbrltype": "tradingSymbolItemType"
    },
    "dei_WrittenCommunications": {
     "auth_ref": [
      "r6"
     ],
     "lang": {
      "en-US": {
       "role": {
        "documentation": "Boolean flag that is true when the Form 8-K filing is intended to satisfy the filing obligation of the registrant as written communications pursuant to Rule 425 under the Securities Act.",
        "label": "Written Communications"
       }
      }
     },
     "localname": "WrittenCommunications",
     "nsuri": "http://xbrl.sec.gov/dei/2019-01-31",
     "presentation": [
      "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation"
     ],
     "xbrltype": "booleanItemType"
    },
    "lmca_DocumentAndEntityInformationAbstract": {
     "auth_ref": [],
     "lang": {
      "en-US": {
       "role": {
        "label": "Document and Entity Information [Abstract]"
       }
      }
     },
     "localname": "DocumentAndEntityInformationAbstract",
     "nsuri": "http://www.libertymedia.com/20191126",
     "xbrltype": "stringItemType"
    },
    "lmca_LibertyBravesGroupCommonClassCMember": {
     "auth_ref": [],
     "lang": {
      "en-US": {
       "role": {
        "documentation": "Classification of Liberty Braves Group common stock that has different rights than provided to Class A or B shares, representing ownership interest in a corporation.",
        "label": "Liberty Braves Group Common Class C [Member]",
        "terseLabel": "Liberty Braves Group Common Class C"
       }
      }
     },
     "localname": "LibertyBravesGroupCommonClassCMember",
     "nsuri": "http://www.libertymedia.com/20191126",
     "presentation": [
      "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation"
     ],
     "xbrltype": "domainItemType"
    },
    "lmca_LibertyBravesGroupCommonClassMember": {
     "auth_ref": [],
     "lang": {
      "en-US": {
       "role": {
        "documentation": "Classification of Liberty Braves Group common stock representing ownership interest in a corporation.",
        "label": "Liberty Braves Group Common Class [Member]",
        "terseLabel": "Liberty Braves Group Common Class A"
       }
      }
     },
     "localname": "LibertyBravesGroupCommonClassMember",
     "nsuri": "http://www.libertymedia.com/20191126",
     "presentation": [
      "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation"
     ],
     "xbrltype": "domainItemType"
    },
    "lmca_LibertyFormulaOneGroupCommonClassCMember": {
     "auth_ref": [],
     "lang": {
      "en-US": {
       "role": {
        "documentation": "Classification of Liberty Formula One Group common stock that has different rights than provided to Class A or B shares, representing ownership interest in a corporation.",
        "label": "Liberty Formula One Group Common Class C [Member]",
        "terseLabel": "Liberty Formula One Group Common Class C"
       }
      }
     },
     "localname": "LibertyFormulaOneGroupCommonClassCMember",
     "nsuri": "http://www.libertymedia.com/20191126",
     "presentation": [
      "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation"
     ],
     "xbrltype": "domainItemType"
    },
    "lmca_LibertyFormulaOneGroupCommonClassMember": {
     "auth_ref": [],
     "lang": {
      "en-US": {
       "role": {
        "documentation": "Classification of Liberty Formula One Group common stock representing ownership interest in a corporation.",
        "label": "Liberty Formula One Group Common Class [Member]",
        "terseLabel": "Liberty Formula One Group Common Class A"
       }
      }
     },
     "localname": "LibertyFormulaOneGroupCommonClassMember",
     "nsuri": "http://www.libertymedia.com/20191126",
     "presentation": [
      "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation"
     ],
     "xbrltype": "domainItemType"
    },
    "lmca_LibertySiriusXmGroupCommonClassBMember": {
     "auth_ref": [],
     "lang": {
      "en-US": {
       "role": {
        "documentation": "Classification of Liberty Sirius XM Group common stock that has different rights than Common Class A, representing ownership interest in a corporation.",
        "label": "Liberty Sirius Xm Group Common Class B [Member]",
        "terseLabel": "Liberty Sirius XM Group Common Class B"
       }
      }
     },
     "localname": "LibertySiriusXmGroupCommonClassBMember",
     "nsuri": "http://www.libertymedia.com/20191126",
     "presentation": [
      "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation"
     ],
     "xbrltype": "domainItemType"
    },
    "lmca_LibertySiriusXmGroupCommonClassCMember": {
     "auth_ref": [],
     "lang": {
      "en-US": {
       "role": {
        "documentation": "Classification of Liberty Sirius XM Group common stock that has different rights than provided to Class A or B shares, representing ownership interest in a corporation.",
        "label": "Liberty Sirius Xm Group Common Class C [Member]",
        "terseLabel": "Liberty Sirius Xm Group Common Class C"
       }
      }
     },
     "localname": "LibertySiriusXmGroupCommonClassCMember",
     "nsuri": "http://www.libertymedia.com/20191126",
     "presentation": [
      "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation"
     ],
     "xbrltype": "domainItemType"
    },
    "lmca_LibertySiriusXmGroupCommonClassMember": {
     "auth_ref": [],
     "lang": {
      "en-US": {
       "role": {
        "documentation": "Classification of Liberty Sirius XM Group common stock representing ownership interest in a corporation.",
        "label": "Liberty Sirius Xm Group Common Class [Member]",
        "terseLabel": "Liberty Sirius XM Group Common Class A"
       }
      }
     },
     "localname": "LibertySiriusXmGroupCommonClassMember",
     "nsuri": "http://www.libertymedia.com/20191126",
     "presentation": [
      "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation"
     ],
     "xbrltype": "domainItemType"
    },
    "us-gaap_ClassOfStockDomain": {
     "auth_ref": [],
     "lang": {
      "en-US": {
       "role": {
        "label": "Class Of Stock [Domain]",
        "terseLabel": "Class of Stock [Domain]"
       }
      }
     },
     "localname": "ClassOfStockDomain",
     "nsuri": "http://fasb.org/us-gaap/2019-01-31",
     "presentation": [
      "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation"
     ],
     "xbrltype": "domainItemType"
    },
    "us-gaap_StatementClassOfStockAxis": {
     "auth_ref": [],
     "lang": {
      "en-US": {
       "role": {
        "label": "Class of Stock [Axis]"
       }
      }
     },
     "localname": "StatementClassOfStockAxis",
     "nsuri": "http://fasb.org/us-gaap/2019-01-31",
     "presentation": [
      "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation"
     ],
     "xbrltype": "stringItemType"
    },
    "us-gaap_StatementLineItems": {
     "auth_ref": [],
     "lang": {
      "en-US": {
       "role": {
        "label": "Statement [Line Items]"
       }
      }
     },
     "localname": "StatementLineItems",
     "nsuri": "http://fasb.org/us-gaap/2019-01-31",
     "presentation": [
      "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation"
     ],
     "xbrltype": "stringItemType"
    },
    "us-gaap_StatementTable": {
     "auth_ref": [],
     "lang": {
      "en-US": {
       "role": {
        "label": "Statement [Table]"
       }
      }
     },
     "localname": "StatementTable",
     "nsuri": "http://fasb.org/us-gaap/2019-01-31",
     "presentation": [
      "http://www.libertymedia.com/role/DocumentDocumentAndEntityInformation"
     ],
     "xbrltype": "stringItemType"
    }
   },
   "unitCount": 0
  }
 },
 "std_ref": {
  "r0": {
   "Name": "Exchange Act",
   "Number": "240",
   "Publisher": "SEC",
   "Section": "12",
   "Subsection": "b"
  },
  "r1": {
   "Name": "Exchange Act",
   "Number": "240",
   "Publisher": "SEC",
   "Section": "12",
   "Subsection": "d1-1"
  },
  "r2": {
   "Name": "Exchange Act",
   "Number": "240",
   "Publisher": "SEC",
   "Section": "13e",
   "Subsection": "4c"
  },
  "r3": {
   "Name": "Exchange Act",
   "Number": "240",
   "Publisher": "SEC",
   "Section": "14d",
   "Subsection": "2b"
  },
  "r4": {
   "Name": "Exchange Act",
   "Number": "240",
   "Publisher": "SEC",
   "Section": "14a",
   "Subsection": "12"
  },
  "r5": {
   "Name": "Regulation 12B",
   "Number": "240",
   "Publisher": "SEC",
   "Section": "12",
   "Subsection": "b-2"
  },
  "r6": {
   "Name": "Securities Act",
   "Number": "230",
   "Publisher": "SEC",
   "Section": "425"
  }
 },
 "version": "2.1"
}
</TEXT>
</DOCUMENT>
</SEC-DOCUMENT>
