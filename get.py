import random
import shutil
from pathlib import Path

# Set source and destination directories
SRC_DIR = Path('sec-edgar-filings')
DST_DIR = Path('extract')
DST_DIR.mkdir(exist_ok=True)

# Recursively find all files in sec-edgar-filings
all_files = [f for f in SRC_DIR.rglob('*') if f.is_file()]

if len(all_files) < 15:
    raise ValueError(f"Not enough files in {SRC_DIR} to select 15 random files.")

selected_files = random.sample(all_files, 15)

for src_file in selected_files:
    dst_file = DST_DIR / src_file.name
    shutil.copy2(src_file, dst_file)
    print(f"Copied {src_file} to {dst_file}")
