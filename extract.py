import re
from typing import Dict, Any, <PERSON>, <PERSON><PERSON>

def parse_sec_file(filepath: str) -> Tuple[Dict[str, Any], List[Dict[str, Any]]]:
  sec_header_patterns = [
    ('acceptence_datetime', re.compile(rb"\s*<ACCEPTANCE-DATETIME>\s*(\d{14})")),
    ('accession_number', re.compile(rb"\s*ACCESSION NUMBER:\s*(\S+)")),
    ('conformed_submission_type', re.compile(rb"\s*CONFORMED SUBMISSION TYPE:\s*(\S+)")),
    ('public_document_count', re.compile(rb"\s*PUBLIC DOCUMENT COUNT:\s*(\d+)")),
    ('conformed_period_of_report', re.compile(rb"\s*CONFORMED PERIOD OF REPORT:\s*(\d{8})")),
    ('filing_date', re.compile(rb"\s*FILED AS OF DATE:\s*(\d{8})")),
    ('amendment_date', re.compile(rb"\s*DATE AS OF CHANGE:\s*(\d{8})")),
    ('company_conformed_name', re.compile(rb"\s*COMPANY CONFORMED NAME:\s*(.+)")),
    ('cik', re.compile(rb"\s*CENTRAL INDEX KEY:\s*(\d+)")),
  ]

  document_patterns = [
    ('document_type', re.compile(rb"\s*<TYPE>\s*(\S+)")),
    ('filename', re.compile(rb"\s*<FILENAME>\s*(\S+)")),
    ('description', re.compile(rb"\s*<DESCRIPTION>\s*(.+)")),
  ]

  document_start_pattern = re.compile(rb"<DOCUMENT>")
  document_end_pattern = re.compile(rb"</DOCUMENT>")

  results: Dict[str, Any] = {}
  documents: List[Dict[str, Any]] = []

  current_location = 'header'
  in_document = False
  temp_document: Dict[str, Any] = {}

  header_iter = iter(sec_header_patterns)
  doc_iter = iter(document_patterns)

  try:
    header_key, header_pattern = next(header_iter)
  except StopIteration:
    header_key, header_pattern = None, None

  try:
    doc_key, doc_pattern = next(doc_iter)
  except StopIteration:
    doc_key, doc_pattern = None, None

  with open(filepath, 'rb') as f:
    for line in f:
      if current_location == 'header' and header_pattern:
        match = header_pattern.match(line)
        if match:
          results[header_key] = match.group(1).decode()
          try:
            header_key, header_pattern = next(header_iter)
          except StopIteration:
            header_key, header_pattern = None, None
            current_location = 'documents'

      elif current_location == 'documents':
        if in_document:
          if document_end_pattern.match(line):
            documents.append(temp_document)
            temp_document = {}
            in_document = False
            doc_iter = iter(document_patterns)
            try:
              doc_key, doc_pattern = next(doc_iter)
            except StopIteration:
              doc_key, doc_pattern = None, None
          elif doc_pattern:
            match = doc_pattern.match(line)
            if match:
              temp_document[doc_key] = match.group(1).decode()
              try:
                doc_key, doc_pattern = next(doc_iter)
              except StopIteration:
                doc_key, doc_pattern = None, None
        elif document_start_pattern.match(line):
          in_document = True
          temp_document = {}

  return results, documents


results, documents = parse_sec_file("a:/DataAggregator/PR/extract/data/CBZ_8-K_20060523_Agreement+Events+Exhibits.txt")
print(results)
print(documents)
